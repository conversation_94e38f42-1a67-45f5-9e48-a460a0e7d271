import 'dart:async';

import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pharmalink/app/my_app.dart';
import 'package:pharmalink/app/service/storage_service.dart';
import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_init.dart';
import 'package:pharmalink/config/dependency_injection.dart';

Future<void> main() async {
  runZonedGuarded<Future<void>>(
    () async {
      WidgetsFlutterBinding.ensureInitialized();

      await DependencyInjection.init();
      await initialConfig();

      await SqfLiteHub.init();

      runApp(const MyAppPL());
    },
    (error, stack) {
      dynatrace.reportZoneStacktrace(error:error, stacktrace: stack);
      appError(error, stack);
      error.printError();
    },
  );
}
