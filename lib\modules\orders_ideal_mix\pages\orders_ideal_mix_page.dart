import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders_ideal_mix/pages/widgets/orders_ideal_mix_product_item_widget.dart';

class OrdersIdealMixPage extends StatelessWidget {
  const OrdersIdealMixPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<OrdersIdealMixController>(
      "Orders Ideal Mix",
      builder: (ctrl) {
        return Scaffold(
          backgroundColor: whiteColor,
          appBar: AppBar(
            backgroundColor: themesController.getPrimaryColor(),
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                LabelWidget(
                  title:
                      ordersController.isEditOrder == true
                          ? ordersController.pdvName!
                          : globalParams.getCurrentStore()?.razaoSocial ?? "-",
                  fontSize: DeviceSize.fontSize(14, 18),
                  fontWeight: FontWeight.w600,
                  textColor: whiteColor,
                ),
                LabelWidget(
                  title:
                      ordersController.isEditOrder == true
                          ? ordersController.pdvCnpj!
                          : globalParams.getCurrentStore()?.cNPJ ?? "-",
                  fontSize: DeviceSize.fontSize(11, 13),
                  textColor: whiteColor,
                ),
              ],
            ),
            actions: [
              CustomInkWell(
                onTap: () async => await ctrl.advance(),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 16,
                  ),
                  child: LabelWidget(
                    title: "Avançar".toUpperCase(),
                    fontSize: 16.sp,
                    textColor: whiteColor,
                  ),
                ),
              ),
            ],
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: whiteColor),
              onPressed: () {
                GetC.close();
              },
            ),
          ),
          body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  OrdersCardInfoWidget(
                    orderType: globalParams.getTypeOrderName(),
                    tabloidName: globalParams.order.tabloidName,
                    distributors:
                        globalParams.order.currentDistributors
                            ?.map(
                              (e) =>
                                  e.distribuidor?.razaoSocial ??
                                  e.distribuidor?.nomeFantasia ??
                                  "Sem identificação",
                            )
                            .toList(),
                    paymentType: globalParams.order.deadlinePayment?.descricao,
                    showMixIdealButton: false,
                  ),
                  LabelWidget(
                    title: "Mix Ideal",
                    fontSize: 24.sp,
                    fontWeight: FontWeight.bold,
                  ),
                  20.toHeightSpace(),
                  ...ctrl.dataList!.condicoes!.map((e) {
                    return SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: Card(
                        elevation: 5,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: LabelWidget(
                                title: e.descricao!,
                                fontSize: 15.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            ExpansionTile(
                              title: LabelWidget(
                                title: "Ver produtos",
                                fontSize: 14.sp,
                                fontWeight: FontWeight.bold,
                              ),
                              children: [
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 15.w,
                                  ),
                                  child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: LabelWidget(
                                      title: "Produtos Mix",
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                ...e.produtosMix!.map((pm) {
                                  return Column(
                                    children: [
                                      ListTile(
                                        title: LabelWidget(
                                          title:
                                              "Família: ${pm.descricaoFamilia!}",
                                          fontSize: 15.sp,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 16.w,
                                        ),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Align(
                                              alignment: Alignment.centerRight,
                                              child: LabelWidget(
                                                title:
                                                    "Limite de unidades totais (Mix): *${e.possuiQuantidadeMaximaMix == true ? "${e.qtdySelected ?? 0}/${e.quantidadeMaximaMix}" : "Sem limite"}*",
                                                fontSize: 14.sp,
                                              ),
                                            ),
                                            Visibility(
                                              visible: ctrl
                                                  .isFamilyMixShowLimit(pm),
                                              child: const Gap(20),
                                            ),
                                            Visibility(
                                              visible: ctrl
                                                  .isFamilyMixShowLimit(pm),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Flexible(
                                                    child: LabelWidget(
                                                      title:
                                                          "Qtd. Mínima: ${pm.quantidadeMinima ?? "-"}",
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 12.sp,
                                                    ),
                                                  ),
                                                  Flexible(
                                                    child: LabelWidget(
                                                      title:
                                                          "Qtd. Máxima: ${pm.quantidadeMaxima ?? "-"}",
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 12.sp,
                                                    ),
                                                  ),
                                                  Flexible(
                                                    child: LabelWidget(
                                                      title:
                                                          "Desc: ${pm.desconto?.formatPercent() ?? "-"}",
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 12.sp,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      ...pm.produtos!.map(
                                        (p) => OrdersIdealMixProductItemWidget(
                                          familyName: pm.descricaoFamilia!,
                                          discount: pm.desconto,
                                          minQtdy: pm.quantidadeMinima,
                                          showQtdyMin: true,
                                          showQtdyMax: true,
                                          item: p,
                                          mixIdealCondicoes: e,
                                          familyMix: pm,
                                        ),
                                      ),
                                    ],
                                  );
                                }),
                                Visibility(
                                  visible: e.produtosCondicao!.isNotEmpty,
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 15.w,
                                    ),
                                    child: Align(
                                      alignment: Alignment.centerLeft,
                                      child: LabelWidget(
                                        title: "Produtos Condição",
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                                ...e.produtosCondicao!.map((pm) {
                                  return Column(
                                    children: [
                                      ListTile(
                                        title: LabelWidget(
                                          title:
                                              "Família: ${pm.descricaoFamilia!}",
                                          fontSize: 15.sp,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 16.w,
                                        ),
                                        child: Align(
                                          alignment: Alignment.centerRight,
                                          child: LabelWidget(
                                            title:
                                                "Limite de unidades totais (Condição): *${e.possuiQuantidadeMaximaCondicao == true ? "${e.qtdyCondicaoSelected ?? 0}/${e.quantidadeMaximaCondicao}" : "Sem limite"}*",
                                            fontSize: 14.sp,
                                          ),
                                        ),
                                      ),
                                      const Gap(10),
                                      Visibility(
                                        visible:
                                            pm.quantidadeMaxima != null ||
                                            pm.desconto != null,
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceAround,
                                          children: [
                                            LabelWidget(
                                              title:
                                                  "Qtd Máxima: ${pm.quantidadeMaxima?.toString() ?? "-"}",
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.bold,
                                            ),
                                            LabelWidget(
                                              title:
                                                  "Desconto: ${pm.desconto?.formatPercent() ?? "-"}",
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ],
                                        ),
                                      ),
                                      ...pm.produtos!.map(
                                        (p) => OrdersIdealMixProductItemWidget(
                                          familyName: pm.descricaoFamilia!,
                                          showQtdyMax: true,
                                          showQtdyMin: false,
                                          item: p,
                                          mixIdealCondicoes: e,
                                          familyConditionalMix: pm,
                                        ),
                                      ),
                                    ],
                                  );
                                }),
                              ],
                            ),
                          ],
                        ),
                      ),
                    );
                  }),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
