import 'dart:convert';

import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/core/encrypt/hash_helper.dart';
import 'package:pharmalink/modules/order_types/models/order_parameters_model.dart';
import 'package:pharmalink/modules/orders_resume/models/file_attachment.dart';
import 'package:pharmalink/modules/orders_resume/models/orders_resume_model.dart';
import 'package:pharmalink/modules/orders_resume/models/system_info_model.dart';
import 'package:pharmalink/modules/stores/models/commercial_condition_model.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';

class SyncronizationModel extends SqfLiteBase<SyncronizationModel> {
  OrdersSyncModel? payLoad;
  int? status;
  int? step;
  String? userId;
  String? transactionKey;
  String? response;
  int? workSpaceId;
  int? idUser;
  int? type;
  DateTime? createAt;
  OrderParametersModel? parameters;
  bool? isOnline;
  StoresModel? storeData;
  int? typeOrder;
  bool? isSelected;
  FileAttachment? fileAttachment;
  CommercialConditionModel? commercialConditionParameter;
  SyncronizationModel({
    this.payLoad,
    this.status,
    this.step,
    this.userId,
    this.transactionKey,
    this.response,
    this.workSpaceId,
    this.idUser,
    this.type,
    this.createAt,
    this.parameters,
    this.isOnline,
    this.storeData,
    this.typeOrder,
    this.isSelected,
    this.fileAttachment,
    this.commercialConditionParameter,
  }) : super(DatabaseModels.syncronization);

  SyncronizationModel.fromJson(Map<String, dynamic> json)
    : super(DatabaseModels.syncronization) {
    status = json['status'];
    step = json['step'];
    userId = json['userId'];
    transactionKey = json['transaction_key'];
    response = json['response'];
    workSpaceId = json['workSpaceId'];
    idUser = json['idUser'];
    type = json['type'];
    createAt =
        json['createAt'] != null ? DateTime.parse(json['createAt']) : null;
    payLoad =
        json['payLoad'] != null
            ? OrdersSyncModel.fromJson(json['payLoad'])
            : null;
    parameters =
        json['parameters'] != null
            ? OrderParametersModel.fromJson(json['parameters'])
            : null;
    isOnline = json['isOnline'] ?? true;
    storeData =
        json['storeData'] != null
            ? StoresModel.fromJson(json['storeData'])
            : null;
    typeOrder = json['typeOrder'];
    isSelected = false;
    fileAttachment =
        json['fileAttachment'] != null
            ? FileAttachment.fromJson(json['fileAttachment'])
            : null;
    commercialConditionParameter =
        json['commercialConditionParameter'] != null
            ? CommercialConditionModel.fromJson(
              json['commercialConditionParameter'],
            )
            : null;
  }

  Map<String, dynamic> toJson() {
    return {
      'payLoad': payLoad?.toJson(),
      'status': status,
      'step': step,
      'userId': userId,
      'transaction_key': transactionKey,
      'response': response,
      'workSpaceId': workSpaceId,
      'idUser': idUser,
      'type': type,
      'createAt': createAt?.toIso8601String(),
      'parameters': parameters?.toJson(),
      'isOnline': isOnline,
      'storeData': storeData?.toJson(),
      'typeOrder': typeOrder,
      'fileAttachment': fileAttachment?.toJson(),
      'commercialConditionParameter': commercialConditionParameter?.toJson(),
    };
  }

  /// Verifica se o pedido tem erro de sincronização
  bool hasError() {
    return status == 3 || status == 8; // receiveWithError ou receiveAuthError
  }

  /// Extrai a mensagem de erro do campo response
  String? getErrorMessage() {
    if (!hasError() || response == null || response!.isEmpty) {
      return null;
    }

    try {
      // Tenta parsear como JSON primeiro
      final Map<String, dynamic> responseJson = jsonDecode(response!);

      // Verifica se tem estrutura de erro padrão
      if (responseJson.containsKey('MessageNo')) {
        return responseJson['MessageNo'];
      }

      if (responseJson.containsKey('message')) {
        return responseJson['message'];
      }

      if (responseJson.containsKey('error')) {
        return responseJson['error'];
      }

      // Se não encontrou campos específicos, retorna o response como string
      return response;
    } catch (e) {
      // Se não conseguir parsear como JSON, retorna o response como string
      return response;
    }
  }

  Future<SyncronizationModel> getFirst({
    required int workspaceId,
    int? storeId,
  }) async {
    var list = await getAll<SyncronizationModel>(
      workspaceId: workspaceId,
      storeId: storeId,
      userId: appController.userLogged!.userId,
      SyncronizationModel.fromJson,
    );
    return list.first;
  }

  Future<List<SyncronizationModel>> getList({
    required int workspaceId,
    int? storeId,
  }) async {
    var list = await getAll<SyncronizationModel>(
      workspaceId: workspaceId,
      userId: appController.userLogged!.userId,
      storeId: storeId,
      SyncronizationModel.fromJson,
    );
    return list;
  }

  Future<bool> exists({
    required int workspaceId,
    int? storeId,
    DateTime? today,
  }) async {
    var list = await getAll<SyncronizationModel>(
      workspaceId: workspaceId,
      storeId: storeId,
      userId: appController.userLogged!.userId,
      SyncronizationModel.fromJson,
    );
    if (today != null) {
      var dateToday = DateTime(today.year, today.month, today.day);
      return list.isNotEmpty
          ? list.any((element) {
            if (element.createAt == null) return false;
            var elementDate = DateTime(
              element.createAt!.year,
              element.createAt!.month,
              element.createAt!.day,
            );
            return elementDate == dateToday;
          })
          : false;
    } else {
      return false;
    }
  }

  String toJsonHash() {
    return HashCreator.generateHash(jsonEncode(this));
  }
}

class OrdersSyncModel {
  String? userId;
  String? orderId;
  int? workSpaceId;
  int? pdvId;
  String? pdvName;
  String? pdvCnpj;
  int? orderStatus;
  DateTime? createAt;
  List<OrdersDataModel>? orderInfo;

  OrdersResumeModel? backupOrder;
  bool? isOnline;
  OrdersSyncModel({
    this.userId,
    this.orderId,
    this.workSpaceId,
    this.pdvId,
    this.pdvName,
    this.pdvCnpj,
    this.orderStatus,
    this.createAt,
    this.orderInfo,
    this.backupOrder,
    this.isOnline,
  });

  OrdersSyncModel.fromJson(Map<String, dynamic> json) {
    userId = json['userId'];
    orderId = json['orderId'];
    workSpaceId = json['workSpaceId'];
    pdvId = json['pdvId'];
    pdvName = json['pdvName'];
    pdvCnpj = json['pdvCnpj'];
    orderStatus = json['orderStatus'];
    createAt =
        json['createAt'] != null ? DateTime.parse(json['createAt']) : null;
    if (json['orderInfo'] != null) {
      orderInfo = <OrdersDataModel>[];
      json['orderInfo'].forEach((v) {
        orderInfo!.add(OrdersDataModel.fromJson(v));
      });
    }
    backupOrder =
        json['backupOrder'] != null
            ? OrdersResumeModel.fromJson(json['backupOrder'])
            : null;
    isOnline = json['isOnline'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['userId'] = userId;
    data['orderId'] = orderId;
    data['workSpaceId'] = workSpaceId;
    data['pdvId'] = pdvId;
    data['pdvName'] = pdvName;
    data['pdvCnpj'] = pdvCnpj;
    data['orderStatus'] = orderStatus;
    data['createAt'] = createAt?.toIso8601String();
    if (orderInfo != null) {
      data['orderInfo'] = orderInfo!.map((v) => v.toJson()).toList();
    }

    if (backupOrder != null) {
      data['backupOrder'] = backupOrder!.toJson();
    }
    data['isOnline'] = isOnline;
    return data;
  }
}

class OrdersDataModel {
  String? userId;
  String? orderId;
  String? workSpace;
  OrdersSyncAuthorizationModel? authorization;
  OrdersSyncAuthorizationWithTokenModel? authorizationWithToken;
  String? authorizationUri;
  String? timeStamp;
  DateTime? createAt;
  int? orderStatus;
  String? verb;
  String? payLoadUrl;
  String? industryDomain;
  List<OrdersSyncPayLoadModel>? payLoad;
  OrdersSyncPushNotificationModel? pushNotification;
  bool? forApproval;
  SystemInfoModel? systemInfo;

  OrdersDataModel({
    this.userId,
    this.orderId,
    this.workSpace,
    this.authorization,
    this.authorizationWithToken,
    this.authorizationUri,
    this.timeStamp,
    this.orderStatus,
    this.verb,
    this.createAt,
    this.payLoadUrl,
    this.industryDomain,
    this.payLoad,
    this.pushNotification,
    this.forApproval,
    this.systemInfo,
  });

  OrdersDataModel.fromJson(Map<String, dynamic> json) {
    userId = json['userId'];
    orderId = json['orderId'];
    workSpace = json['workSpace'];
    authorization =
        json['authorization'] != null
            ? OrdersSyncAuthorizationModel.fromJson(json['authorization'])
            : null;
    authorizationWithToken =
        json['authorizationWithToken'] != null
            ? OrdersSyncAuthorizationWithTokenModel.fromJson(
              json['authorizationWithToken'],
            )
            : null;

    createAt =
        json['createAt'] != null ? DateTime.parse(json['createAt']) : null;
    authorizationUri = json['authorizationUri'];
    timeStamp = json['timeStamp'];
    if (json['orderStatus'] is String) {
      orderStatus = int.parse(json['orderStatus']);
    } else if (json['orderStatus'] is int) {
      orderStatus = json['orderStatus'];
    }
    verb = json['verb'];
    payLoadUrl = json['payLoadUrl'];
    industryDomain = json['IndustryDomain'];
    if (json['payLoad'] != null) {
      payLoad = <OrdersSyncPayLoadModel>[];
      json['payLoad'].forEach((v) {
        payLoad!.add(OrdersSyncPayLoadModel.fromJson(v));
      });
    }
    pushNotification =
        json['pushNotification'] != null
            ? OrdersSyncPushNotificationModel.fromJson(json['pushNotification'])
            : null;
    forApproval = json['forApproval'];
    systemInfo =
        json['systemInfo'] != null
            ? SystemInfoModel.fromJson(json['systemInfo'])
            : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['userId'] = userId;
    data['orderId'] = orderId;
    data['workSpace'] = workSpace;
    if (authorization != null) {
      data['authorization'] = authorization!.toJson();
    }
    if (authorizationWithToken != null) {
      data['authorizationWithToken'] = authorizationWithToken!.toJson();
    }
    data['authorizationUri'] = authorizationUri;
    data['timeStamp'] = timeStamp;
    data['orderStatus'] = orderStatus;
    data['verb'] = verb;
    data['payLoadUrl'] = payLoadUrl;
    data['IndustryDomain'] = industryDomain;
    if (payLoad != null) {
      data['payLoad'] = payLoad!.map((v) => v.toJson()).toList();
    }
    if (pushNotification != null) {
      data['pushNotification'] = pushNotification!.toJson();
    }
    data['forApproval'] = forApproval;
    data['createAt'] = createAt?.toIso8601String();
    if (systemInfo != null) {
      data['systemInfo'] = systemInfo!.toJson();
    }
    return data;
  }

  Map<String, dynamic> toJsonApi() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['userId'] = userId;
    data['orderId'] = orderId;
    data['workSpace'] = workSpace;
    if (authorization != null) {
      data['authorization'] = authorization!.toJson();
    }
    if (authorizationWithToken != null) {
      data['authorizationWithToken'] = authorizationWithToken!.toJson();
    }
    data['authorizationUri'] = authorizationUri;
    data['timeStamp'] = timeStamp;
    data['orderStatus'] = orderStatus;
    data['verb'] = verb;
    data['payLoadUrl'] = payLoadUrl;
    data['IndustryDomain'] = industryDomain;
    if (payLoad != null) {
      data['payLoad'] = payLoad!.map((v) => v.toJson()).toList();
    }
    if (pushNotification != null) {
      data['pushNotification'] = pushNotification!.toJson();
    }
    data['forApproval'] = forApproval;
    data['createAt'] = createAt?.toIso8601String();
    if (systemInfo != null) {
      data['systemInfo'] = systemInfo!.toJson();
    }
    return data;
  }
}

class OrdersSyncAuthorizationModel {
  String? user;
  String? password;

  OrdersSyncAuthorizationModel({this.user, this.password});

  OrdersSyncAuthorizationModel.fromJson(Map<String, dynamic> json) {
    user = json['user'];
    password = json['password'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['user'] = user;
    data['password'] = password;
    return data;
  }
}

class OrdersSyncAuthorizationWithTokenModel {
  String? token;
  String? clientKey;

  OrdersSyncAuthorizationWithTokenModel({this.token, this.clientKey});

  OrdersSyncAuthorizationWithTokenModel.fromJson(Map<String, dynamic> json) {
    token = json['token'];
    clientKey = json['clientKey'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['token'] = token;
    data['clientKey'] = clientKey;
    return data;
  }
}

class OrdersSyncPayLoadModel {
  bool? isPedidoComboOferta;
  int? idComboOferta;
  String? descricaoComboOferta;
  String? agrupadorPedido;
  String? cNPJ;
  OrdersSyncDistribuidoresModel? distribuidor;
  List<OrdersSyncDistribuidoresModel>? distribuidores;
  int? distribuidorId;

  List<OrdersSyncItensDoPedidoModel>? itensDoPedido;
  List<OrdersSyncCombosDoPedidoModel>? combosDoPedido;
  int? condicaoComercialBaseId;
  int? idTabloide;
  List<String>? datasProgramadas;
  bool? forcarGerenciamento;
  int? idPrazoPagamento;
  String? codigo;
  String? descricao;
  String? origem;
  int? pdvId;
  String? prazo;
  String? somenteValidar;
  String? tipoLooping;
  String? tipoPedido;
  String? userId;
  String? chaveUnicaPedido;
  String? referenciaPedido;
  String? referenciaDePedido;
  List<OrdersSyncProdutosComboModel>? produtosCombo;
  int? totalApresentacoesSKU;
  int? totalUnidadesSKU;
  int? descontoTotalSKU;
  int? descontoRecebidoSKU;
  double? totalBrutoSKU;
  double? totalLiquidoSKU;
  String? observacao;
  String? razaoSocial;

  OrdersSyncPayLoadModel({
    this.isPedidoComboOferta,
    this.idComboOferta,
    this.descricaoComboOferta,
    this.agrupadorPedido,
    this.cNPJ,
    this.distribuidor,
    this.distribuidores,
    this.distribuidorId,
    this.itensDoPedido,
    this.combosDoPedido,
    this.condicaoComercialBaseId,
    this.idTabloide,
    this.datasProgramadas,
    this.forcarGerenciamento,
    this.idPrazoPagamento,
    this.codigo,
    this.descricao,
    this.origem,
    this.pdvId,
    this.prazo,
    this.somenteValidar,
    this.tipoLooping,
    this.tipoPedido,
    this.userId,
    this.chaveUnicaPedido,
    this.referenciaPedido,
    this.referenciaDePedido,
    this.produtosCombo,
    this.totalApresentacoesSKU,
    this.totalUnidadesSKU,
    this.descontoTotalSKU,
    this.descontoRecebidoSKU,
    this.totalBrutoSKU,
    this.totalLiquidoSKU,
    this.observacao,
    this.razaoSocial,
  });

  OrdersSyncPayLoadModel.fromJson(Map<String, dynamic> json) {
    isPedidoComboOferta = json['IsPedidoComboOferta'];
    idComboOferta = json['IdComboOferta'];
    descricaoComboOferta = json['DescricaoComboOferta'];
    agrupadorPedido = json['AgrupadorPedido'];
    cNPJ = json['CNPJ'];
    distribuidor =
        json['Distribuidor'] != null
            ? OrdersSyncDistribuidoresModel.fromJson(json['Distribuidor'])
            : null;
    if (json['Distribuidores'] != null) {
      distribuidores = <OrdersSyncDistribuidoresModel>[];
      json['Distribuidores'].forEach((v) {
        distribuidores!.add(OrdersSyncDistribuidoresModel.fromJson(v));
      });
    }
    distribuidorId = json['DistribuidorId'];
    if (json['ItensDoPedido'] != null) {
      itensDoPedido = <OrdersSyncItensDoPedidoModel>[];
      json['ItensDoPedido'].forEach((v) {
        itensDoPedido!.add(OrdersSyncItensDoPedidoModel.fromJson(v));
      });
    }
    if (json['CombosDoPedido'] != null) {
      combosDoPedido = <OrdersSyncCombosDoPedidoModel>[];
      json['CombosDoPedido'].forEach((v) {
        combosDoPedido!.add(OrdersSyncCombosDoPedidoModel.fromJson(v));
      });
    }
    condicaoComercialBaseId = json['CondicaoComercialBaseId'];
    idTabloide = json['IdTabloide'];
    datasProgramadas = json['DatasProgramadas'].cast<String>();
    forcarGerenciamento = json['ForcarGerenciamento'];
    idPrazoPagamento = json['IdPrazoPagamento'];
    codigo = json['Codigo'];
    descricao = json['Descricao'];
    origem = json['Origem'];
    pdvId = json['PdvId'];
    prazo = json['Prazo'];
    somenteValidar = json['SomenteValidar'];
    tipoLooping = json['TipoLooping'];
    tipoPedido = json['TipoPedido'];
    userId = json['UserId'];
    chaveUnicaPedido = json['ChaveUnicaPedido'];
    referenciaPedido = json['ReferenciaPedido'];
    referenciaDePedido = json['ReferenciaDePedido'];
    if (json['ProdutosCombo'] != null) {
      produtosCombo = <OrdersSyncProdutosComboModel>[];
      json['ProdutosCombo'].forEach((v) {
        produtosCombo!.add(OrdersSyncProdutosComboModel.fromJson(v));
      });
    }
    totalApresentacoesSKU = json['totalApresentacoesSKU'];
    totalUnidadesSKU = json['totalUnidadesSKU'];
    descontoTotalSKU = json['descontoTotalSKU'];
    descontoRecebidoSKU = json['descontoRecebidoSKU'];
    totalBrutoSKU = json['totalBrutoSKU'];
    totalLiquidoSKU = json['totalLiquidoSKU'];
    observacao = json['Observacao'];
    razaoSocial = json['RazaoSocial'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IsPedidoComboOferta'] = isPedidoComboOferta;
    data['IdComboOferta'] = idComboOferta;
    data['DescricaoComboOferta'] = descricaoComboOferta;
    data['AgrupadorPedido'] = agrupadorPedido;
    data['CNPJ'] = cNPJ;
    if (distribuidor != null) {
      data['Distribuidor'] = distribuidor!.toJson();
    }
    if (distribuidores != null) {
      data['Distribuidores'] = distribuidores!.map((v) => v.toJson()).toList();
    }
    data['DistribuidorId'] = distribuidorId;
    if (itensDoPedido != null) {
      data['ItensDoPedido'] = itensDoPedido!.map((v) => v.toJson()).toList();
    }
    if (combosDoPedido != null) {
      data['CombosDoPedido'] = combosDoPedido!.map((v) => v.toJson()).toList();
    } else {
      data['CombosDoPedido'] = [];
    }
    data['CondicaoComercialBaseId'] = condicaoComercialBaseId;
    data['IdTabloide'] = idTabloide;
    data['DatasProgramadas'] = datasProgramadas;

    data['ForcarGerenciamento'] = forcarGerenciamento;
    data['IdPrazoPagamento'] = idPrazoPagamento;
    data['Codigo'] = codigo;
    data['Descricao'] = descricao;
    data['Origem'] = origem;
    data['PdvId'] = pdvId;
    data['Prazo'] = prazo;
    data['SomenteValidar'] = somenteValidar;
    data['TipoLooping'] = tipoLooping;
    data['TipoPedido'] = tipoPedido;
    data['UserId'] = userId;
    data['ChaveUnicaPedido'] = chaveUnicaPedido;
    data['ReferenciaPedido'] = referenciaPedido;
    data['ReferenciaDePedido'] = referenciaDePedido;
    if (produtosCombo != null) {
      data['ProdutosCombo'] = produtosCombo!.map((v) => v.toJson()).toList();
    }
    data['totalApresentacoesSKU'] = totalApresentacoesSKU;
    data['totalUnidadesSKU'] = totalUnidadesSKU;
    data['descontoTotalSKU'] = descontoTotalSKU ?? 0;
    data['descontoRecebidoSKU'] = descontoRecebidoSKU ?? 0;
    data['totalBrutoSKU'] = totalBrutoSKU;
    data['totalLiquidoSKU'] = totalLiquidoSKU;
    data['Observacao'] = observacao;
    data['RazaoSocial'] = razaoSocial;
    return data;
  }
}

class OrdersSyncDistribuidorModel {
  int? id;
  bool? ativo;
  double? valorMinimoDePedido;
  String? nomeFantasia;
  String? razaoSocial;
  bool? habilitaContaCorrente;

  OrdersSyncDistribuidorModel({
    this.id,
    this.ativo,
    this.valorMinimoDePedido,
    this.nomeFantasia,
    this.razaoSocial,
    this.habilitaContaCorrente,
  });

  OrdersSyncDistribuidorModel.fromJson(Map<String, dynamic> json) {
    id = json['Id'];
    ativo = json['Ativo'];
    valorMinimoDePedido = json['ValorMinimoDePedido'];
    nomeFantasia = json['NomeFantasia'];
    razaoSocial = json['RazaoSocial'];
    habilitaContaCorrente = json['HabilitaContaCorrente'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Id'] = id;
    data['Ativo'] = ativo;
    data['ValorMinimoDePedido'] = valorMinimoDePedido;
    data['NomeFantasia'] = nomeFantasia;
    data['RazaoSocial'] = razaoSocial;
    data['HabilitaContaCorrente'] = habilitaContaCorrente;
    return data;
  }
}

class OrdersSyncItensDoPedidoModel {
  double? descontoAdicional;
  double? descontoTotal;
  int? idProduto;
  int? quantidade;
  double? preco;
  bool? precoDistribuidor;
  String? dUN;
  int? qtdeDUN;
  int? qtdeProdutoTotal;
  int? idProdutoDUN;
  bool? isDUN;

  OrdersSyncItensDoPedidoModel({
    this.descontoAdicional,
    this.descontoTotal,
    this.idProduto,
    this.quantidade,
    this.preco,
    this.precoDistribuidor,
    this.dUN,
    this.qtdeDUN,
    this.qtdeProdutoTotal,
    this.idProdutoDUN,
    this.isDUN,
  });

  OrdersSyncItensDoPedidoModel.fromJson(Map<String, dynamic> json) {
    descontoAdicional = json['DescontoAdicional'];
    descontoTotal = json['DescontoTotal'];
    idProduto = json['IdProduto'];
    quantidade = json['Quantidade'];
    preco = json['Preco'];
    precoDistribuidor = json['precoDistribuidor'];
    dUN = json['DUN'];
    qtdeDUN = json['QtdeDUN'];
    qtdeProdutoTotal = json['QtdeProdutoTotal'];
    idProdutoDUN = json['idProdutoDUN'];
    isDUN = json['isDUN'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['DescontoAdicional'] = descontoAdicional ?? 0.0;
    data['DescontoTotal'] = descontoTotal ?? 0.0;
    data['IdProduto'] = idProduto;
    data['Quantidade'] = quantidade;
    data['Preco'] = preco;
    data['precoDistribuidor'] = precoDistribuidor;
    data['DUN'] = dUN;
    data['QtdeDUN'] = qtdeDUN;
    data['QtdeProdutoTotal'] = qtdeProdutoTotal;
    if (idProdutoDUN != null) {
      data['idProdutoDUN'] = idProdutoDUN;
    }
    data['isDUN'] = isDUN;
    return data;
  }
}

class OrdersSyncPushNotificationModel {
  String? token;
  OrdersSyncSuccessModel? success;
  OrdersSyncErrorModel? error;

  OrdersSyncPushNotificationModel({this.token, this.success, this.error});

  OrdersSyncPushNotificationModel.fromJson(Map<String, dynamic> json) {
    token = json['Token'];
    success =
        json['Success'] != null
            ? OrdersSyncSuccessModel.fromJson(json['Success'])
            : null;
    error =
        json['Error'] != null
            ? OrdersSyncErrorModel.fromJson(json['Error'])
            : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Token'] = token;
    if (success != null) {
      data['Success'] = success!.toJson();
    }
    if (error != null) {
      data['Error'] = error!.toJson();
    }
    return data;
  }
}

class OrdersSyncSuccessModel {
  String? titleYes;
  String? messageYes;
  OrdersSyncDataYesModel? dataYes;

  OrdersSyncSuccessModel({this.titleYes, this.messageYes, this.dataYes});

  OrdersSyncSuccessModel.fromJson(Map<String, dynamic> json) {
    titleYes = json['TitleYes'];
    messageYes = json['MessageYes'];
    dataYes =
        json['DataYes'] != null
            ? OrdersSyncDataYesModel.fromJson(json['DataYes'])
            : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['TitleYes'] = titleYes;
    data['MessageYes'] = messageYes;
    if (dataYes != null) {
      data['DataYes'] = dataYes!.toJson();
    }
    return data;
  }
}

class OrdersSyncDataYesModel {
  String? orderId;
  String? page;
  OrdersSyncParamsModel? params;

  OrdersSyncDataYesModel({this.orderId, this.page, this.params});

  OrdersSyncDataYesModel.fromJson(Map<String, dynamic> json) {
    orderId = json['orderId'];
    page = json['page'];
    params =
        json['params'] != null
            ? OrdersSyncParamsModel.fromJson(json['params'])
            : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['orderId'] = orderId;
    data['page'] = page;
    if (params != null) {
      data['params'] = params!.toJson();
    }
    return data;
  }
}

class OrdersSyncParamsModel {
  bool? openFromPush;
  String? orderId;

  OrdersSyncParamsModel({this.openFromPush, this.orderId});

  OrdersSyncParamsModel.fromJson(Map<String, dynamic> json) {
    openFromPush = json['openFromPush'];
    orderId = json['orderId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['openFromPush'] = openFromPush;
    data['orderId'] = orderId;
    return data;
  }
}

class OrdersSyncErrorModel {
  String? titleNo;
  String? messageNo;
  //DataNo? dataNo;

  OrdersSyncErrorModel({this.titleNo, this.messageNo});

  OrdersSyncErrorModel.fromJson(Map<String, dynamic> json) {
    titleNo = json['TitleNo'];
    messageNo = json['MessageNo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['TitleNo'] = titleNo;
    data['MessageNo'] = messageNo;

    return data;
  }
}

class OrdersSyncDistribuidoresModel {
  int? pdvId;
  int? distribuidorId;
  int? ordemDePreferencia;
  int? ordemMelhorAtendimento;
  OrdersSyncDistribuidorModel? distribuidor;
  bool? selected;
  int? oldOrdemDePreferencia;

  OrdersSyncDistribuidoresModel({
    this.pdvId,
    this.distribuidorId,
    this.ordemDePreferencia,
    this.ordemMelhorAtendimento,
    this.distribuidor,
    this.selected,
    this.oldOrdemDePreferencia,
  });

  OrdersSyncDistribuidoresModel.fromJson(Map<String, dynamic> json) {
    pdvId = json['PdvId'];
    distribuidorId = json['DistribuidorId'];
    ordemDePreferencia = json['OrdemDePreferencia'];
    ordemMelhorAtendimento = json['OrdemMelhorAtendimento'];
    distribuidor =
        json['Distribuidor'] != null
            ? OrdersSyncDistribuidorModel.fromJson(json['Distribuidor'])
            : null;
    selected = json['Selected'];
    oldOrdemDePreferencia = json['OldOrdemDePreferencia'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['PdvId'] = pdvId;
    data['DistribuidorId'] = distribuidorId;
    data['OrdemDePreferencia'] = ordemDePreferencia;
    data['OrdemMelhorAtendimento'] = ordemMelhorAtendimento;
    if (distribuidor != null) {
      data['Distribuidor'] = distribuidor!.toJson();
    }
    data['Selected'] = selected;
    data['OldOrdemDePreferencia'] = oldOrdemDePreferencia;
    return data;
  }
}

class OrdersSyncCombosDoPedidoModel {
  int? idComboOferta;
  int? quantidade;

  OrdersSyncCombosDoPedidoModel({this.idComboOferta, this.quantidade});

  OrdersSyncCombosDoPedidoModel.fromJson(Map<String, dynamic> json) {
    idComboOferta = json['IdComboOferta'];
    quantidade = json['Quantidade'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdComboOferta'] = idComboOferta;
    data['Quantidade'] = quantidade;
    return data;
  }
}

class OrdersSyncProdutosComboModel {
  int? idProduto;
  String? ean;
  String? descricao;
  String? laboratorio;
  String? familia;
  int? preco;
  int? quantidade;
  int? desconto;
  bool? bonificado;
  int? precoLiquido;
  List<int>? distribuidores;
  List<int>? distribuidoresFlat;
  int? distribuidorQueAtende;
  String? statusDistribuidor;

  OrdersSyncProdutosComboModel({
    this.idProduto,
    this.ean,
    this.descricao,
    this.laboratorio,
    this.familia,
    this.preco,
    this.quantidade,
    this.desconto,
    this.bonificado,
    this.precoLiquido,
    this.distribuidores,
    this.distribuidoresFlat,
    this.distribuidorQueAtende,
    this.statusDistribuidor,
  });

  OrdersSyncProdutosComboModel.fromJson(Map<String, dynamic> json) {
    idProduto = json['IdProduto'];
    ean = json['Ean'];
    descricao = json['Descricao'];
    laboratorio = json['Laboratorio'];
    familia = json['Familia'];
    preco = json['Preco'];
    quantidade = json['Quantidade'];
    desconto = json['Desconto'];
    bonificado = json['Bonificado'];
    precoLiquido = json['PrecoLiquido'];
    distribuidores = json['Distribuidores'].cast<int>();
    distribuidoresFlat = json['DistribuidoresFlat'].cast<int>();
    distribuidorQueAtende = json['DistribuidorQueAtende'];
    statusDistribuidor = json['StatusDistribuidor'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdProduto'] = idProduto;
    data['Ean'] = ean;
    data['Descricao'] = descricao;
    data['Laboratorio'] = laboratorio;
    data['Familia'] = familia;
    data['Preco'] = preco;
    data['Quantidade'] = quantidade;
    data['Desconto'] = desconto;
    data['Bonificado'] = bonificado;
    data['PrecoLiquido'] = precoLiquido;
    data['Distribuidores'] = distribuidores;
    data['DistribuidoresFlat'] = distribuidoresFlat;
    data['DistribuidorQueAtende'] = distribuidorQueAtende;
    data['StatusDistribuidor'] = statusDistribuidor;
    return data;
  }
}
