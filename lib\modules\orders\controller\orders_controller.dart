import 'dart:convert';
import 'dart:developer';

import 'package:flutter/services.dart';
import 'package:pharmalink/core/enuns/product_state_enum.dart';
import 'package:pharmalink/core/models/result_error_model.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/general_parameterization/models/general_settings_order_discount_registration_model.dart';
import 'package:pharmalink/modules/order_types/models/order_parameters_model.dart';
import 'package:pharmalink/modules/orders/controller/orders_controller_part_edit.dart';
import 'package:pharmalink/modules/orders/controller/orders_controller_part_local.dart';
import 'package:pharmalink/modules/orders/models/orders_filter_default_model.dart';
import 'package:pharmalink/modules/orders/models/orders_footer_model.dart';
import 'package:pharmalink/modules/orders/models/orders_mix_product_special_request.dart';
import 'package:pharmalink/modules/orders/models/orders_mix_product_special_response.dart';
import 'package:pharmalink/modules/orders/models/orders_product_filter_alphabetic_model.dart';
import 'package:pharmalink/modules/orders/models/orders_products_list_model.dart';
import 'package:pharmalink/modules/orders/models/orders_products_model.dart';
import 'package:pharmalink/modules/orders/models/orders_tabloid_products_response_model.dart';
import 'package:pharmalink/modules/orders_ideal_mix/models/orders_ideal_mix_model.dart';
import 'package:pharmalink/modules/orders_resume/models/order_sync_model.dart';
import 'package:pharmalink/modules/orders_resume/models/orders_resume_model.dart';
import 'package:pharmalink/modules/stores_parameters/enuns/type_order_enum.dart';
import 'package:pharmalink/modules/stores_parameters/models/distribuidor_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/distributors_model.dart';

class OrdersController extends GetxControllerInstrumentado<OrdersController>
    with TraceableController {
  OrdersController();

  TabloidProductsResponse? productsTabloid;
  GetMixSpecialProductsResponse? productsTabloidSpecial;
  List<ProductFilterDefaultModel> filterDefaults = [];
  List<SyncronizationModel> syncOrders = [];
  SyncronizationModel? syncOrderEdit;
  String filterSelectedString = "";
  List<String> filterIds = [];
  TextEditingController searchController = TextEditingController();
  String? searchFilter;

  bool isItemMinRequired = false;
  bool isItemMaxRequired = false;

  OrdersFooterModel footer = OrdersFooterModel(
    qtyReal: 0,
    totalApresentation: 0,
    totalNet: 0,
    totalUnits: 0,
    totalGross: 0,
    discount: 0,
  );
  List<OrdersProductsListModel> productList = [];
  List<OrdersProductsListModel> productListFull = [];
  List<OrdersProductsListModel> productListFullEdit = [];
  List<OrdersProductsListModel> productListFilteredByFilters = [];

  bool isEditOrder = false;
  List<OrdersResumeProductsItemsModel> editItensOrder = [];
  List<OrdersResumeProductsItemsModel> editItensComboOrder = [];
  List<DistributorsModel> editDistributors = [];
  int? pdvId;
  bool isOfflineData = false;
  String? pdvName;
  String? pdvCnpj;
  String? currendEditOrderId;
  String? selectedLetter;
  List<OrdersProductFilterAlphabeticModel> alphabet =
      OrdersProductFilterAlphabeticModel.getList();

  @override
  void onReady() {
    if (!Get.isRegistered<OrderTypesController>()) {
      Get.lazyPut<OrderTypesController>(
        () => OrderTypesController(),
        fenix: false,
      );
    }
    orderTypesController = Get.find<OrderTypesController>();

    if (!Get.isRegistered<OrdersResumeController>()) {
      Get.lazyPut<OrdersResumeController>(
        () => OrdersResumeController(),
        fenix: true,
      );
    }
    ordersResumeController = Get.find<OrdersResumeController>();

    if (!Get.isRegistered<OrderPaymentTypeController>()) {
      Get.put(OrderPaymentTypeController());
    }
    orderPaymentTypeController = Get.find<OrderPaymentTypeController>();
    super.onReady();
  }

  Future<void> getPdvData() async {
    pdvId = syncOrderEdit?.payLoad!.pdvId!;
    pdvCnpj = syncOrderEdit?.payLoad!.pdvCnpj!;
    pdvName = syncOrderEdit?.payLoad!.pdvName ?? "-";

    final store = storeRoutesPlannedController.storesList.firstWhereOrNull(
      (element) => element.idLoja == pdvId,
    );
    if (store != null) {
      isOfflineData = store.dataExtra?.offlineDateSync != null;
    }
  }

  Future<void> onEditOrder(String orderId) async {
    await OrdersControllerPartEdit.onEditOrder(this, orderId);
    // pdvId = syncOrderEdit?.payLoad!.pdvId!;
    // pdvCnpj = syncOrderEdit?.payLoad!.pdvCnpj!;
    // pdvName = syncOrderEdit?.payLoad!.pdvName ?? "-";
    // isEditOrder = true;
    // update();
  }

  Future<void> initialize() async {
    filterIds = [];
    isEditOrder = false;
    orderTypesController = Get.find<OrderTypesController>();
    ordersResumeController = Get.find<OrdersResumeController>();
    orderPaymentTypeController = Get.find<OrderPaymentTypeController>();

    filterSelectedString = "";
    searchFilter = "";
    footer = OrdersFooterModel(
      qtyReal: 0,
      totalApresentation: 0,
      totalNet: 0,
      totalUnits: 0,
      totalGross: 0,
      discount: 0,
    );

    isOfflineData =
        globalParams.getCurrentStore()!.dataExtra?.offlineDateSync != null;

    // Mostra um diálogo de confirmação
    filterDefaults = await loadGeneralParameters();
    if (globalParams.getTypeOrderId() == TyperOrderEnum.especial) {
      await loadProductsEsp();
    } else {
      await loadProducts();
    }
    await OrdersControllerPartLocal.loadOrderLocal(this);
  }

  Future<void> loadProductsEsp() async {
    double discountPercent = 0;
    double discountMax = 0;
    double discountMin = 0;
    double exactDiscount = 0;
    double roundedDiscount = 0;
    double discount = 0;

    productListFull.clear();

    await loadAlphabet();
    productListFull.addAll(
      productsTabloid!.produtos!
          .where((x) => x.isDemonstraGridPedido == true)
          .map((e) {
            List<OrdersProductsDiscountRangeModel>? discountRanges =
                e.faixasDesconto != null
                    ? e.faixasDesconto!
                        .map(
                          (x) => OrdersProductsDiscountRangeModel(
                            minQtdy: x.quantidadeMinima,
                            maxQtdy: x.quantidadeMaxima,
                            discountMin: x.descontoMinimo,
                            discountMax: x.descontoMaximo,
                          ),
                        )
                        .toList()
                    : [];
            discountPercent = 0;
            discountMax = 0;
            discountMin = 0;
            exactDiscount = 0;
            roundedDiscount = 0;
            discount = 0;
            if (discountRanges.isNotEmpty) {
              discountMin = calculateDiscountMin(discountRanges.first);
              discountPercent = discountMin;
              discountMax = calculateDiscountMax(discountRanges.first);
              exactDiscount = (e.preco! * discountPercent) / 100;
              roundedDiscount = double.parse(
                (exactDiscount + 0.005).toStringAsFixed(2),
              );
              discount = e.preco! - roundedDiscount;
            }

            return OrdersProductsListModel(
              productId: e.idProduto,
              productCode: e.ean,
              productCodeLabel: "EAN",
              paymentTermId: getPaymentTermEsp(e.idProduto!),
              states: getProductEspecialStateEnum(e.idProduto),
              status: e.status,
              destaque: e.destaque,
              distributorId: getProductEspecialDistributor(e.idProduto),
              isActive: true,
              discount: discount,
              totalOrder: (e.preco! - (e.preco! * discountMin) / 100),
              price: e.preco,
              priceOrder: e.preco,
              limitDate: e.menorDataVigencia,
              minQtdy: 0,
              maxQtdy: e.quantidadeEstoque,
              qtdy: 0,
              qtdyController: TextEditingController(text: "0"),
              focusNode: FocusNode(),
              focusNodeDiscountChange: FocusNode(),
              discountPercent: discountPercent,
              discountChange: discountMin,
              discountMax: discountMax,
              discountMin: discountMin,
              name: e.descricao,
              photo: e.caminhoFoto,
              emphasis: e.destaque,
              discountRange: discountRanges,
              discountRangeCurrent: discountRanges.first,
              stock: e.quantidadeEstoque,
              priceDistributor: e.precoDistribuidor ?? false,
              isEditDiscount: false,
              valueController: TextEditingController(
                text: discountPercent.formatarDouble(),
              ),
              discountChangeController: TextEditingController(
                text: discountMax.formatarDouble(),
              ),
              filterIds:
                  e.filtrosPersonalizados?.map((x) => x.codigo!).toList(),
              metricaMdtr: e.metricaMdtr,
              taxSubstitution: e.substituicaoTributaria,
              codigo: e.codigo,
              principioAtivo: e.principioAtivo,
              precoMaximo: e.precoMaximo,
              caixaEmbarque: e.caixaEmbarque,
              showPriceWithSpecialST:
                  getParamOrderDiscount().exibePrecoComSTEspecial,
              showPriceWithRepST: getParamOrderDiscount().exibePrecoComSTRep,
              discountTaxSubstitution: 0,
            );
          }),
    );

    if (isEditOrder == true) {
      for (var item in productListFull) {
        final productEdit = editItensOrder.firstWhereOrNull(
          (e) =>
              e.productId == item.productId &&
              e.productIdDun == item.productDunId,
        );

        if (productEdit != null) {
          item.qtdyController!.text = productEdit.qtdy.toString();
          item.qtdy = productEdit.qtdy;

          item.discountChange = productEdit.discountApply ?? 0;
          item.discountChangeController!.text =
              item.discountChange?.formatarDouble() ?? "0";
        }
      }
      List<TabloidProductsCombosOfertaResponse> combosOfertaEditList = [];

      if (productsTabloid!.combosOferta != null &&
          productsTabloid!.combosOferta!.isNotEmpty) {
        for (var combo in productsTabloid!.combosOferta!) {
          if (editItensComboOrder.any(
            (e) => e.offerComboId == combo.idComboOferta,
          )) {
            final productEdit =
                editItensComboOrder
                    .where((e) => e.offerComboId == combo.idComboOferta)
                    .first;
            combo.qtdy = productEdit.qtdy;

            combosOfertaEditList.add(combo);
          }
        }
        productsTabloid!.combosOferta = combosOfertaEditList;
      }
    }

    for (var item in productListFull) {
      item.focusNode!.addListener(() async {
        if (item.focusNode!.hasFocus) {
          item.qtdyController!.selection = TextSelection(
            baseOffset: 0,
            extentOffset: item.qtdyController!.text.length,
          );
        } else if (!item.focusNode!.hasFocus) {
          if (item.qtdyController!.text.isNotEmpty) {
            await setQtdyEdit(item, int.parse(item.qtdyController!.text));
          }
        }
      });
      item.focusNodeDiscountChange!.addListener(() async {
        if (item.focusNodeDiscountChange!.hasFocus) {
          item.discountChangeController!.selection = TextSelection(
            baseOffset: 0,
            extentOffset: item.discountChangeController!.text.length,
          );
        } else if (!item.focusNodeDiscountChange!.hasFocus) {
          if (item.discountChangeController!.text.isNotEmpty) {
            //   setDiscountAddEsp(item, item.discountChangeController!.text);
          }
        }
      });
      // item.discountChangeController
    }
    for (var item in productListFull) {
      await calculateDiscountByItem(item);
      setFiltersItens(item);
    }
    productListFull.sort((a, b) => a.name!.compareTo(b.name!));
    productList = productListFull;
    productListFilteredByFilters = List.from(productListFull);
    if (!isEditOrder) {
      update();
    } else {
      for (var item in productListFull) {
        setItemFilterResume(item, item.qtdy == 0);
        await calculateDiscountByItem(item);
      }
      updateFooter();
    }
  }

  int? getPaymentTerm(List<ProdutosDistribuidoresModel> list, int productId) {
    return list.any((e) => e.idProduto == productId)
        ? list.where((e) => e.idProduto == productId).first.idPrazoPagamento
        : null;
  }

  int? getPaymentTermEsp(int productId) {
    return productsTabloidSpecial!.produtosDistribuidores!.any(
          (e) => e.idProduto == productId,
        )
        ? productsTabloidSpecial!.produtosDistribuidores!
            .where((e) => e.idProduto == productId)
            .first
            .idPrazoPagamento
        : null;
  }

  Future<void> loadProducts() async {
    double discountPercent = 0;
    double discountMax = 0;
    double discountMin = 0;
    double exactDiscount = 0;
    double roundedDiscount = 0;
    double discount = 0;
    double discountAvailable = 0;
    double discountChange = 0;
    double discountTotal = 0;

    if (globalParams.getProducts()!.produtos == null &&
        globalParams.getProducts()!.produtos!.skus!.isEmpty &&
        globalParams.getProducts()!.produtos!.combosOferta!.isEmpty) {
      Dialogs.info(
        AppStrings.attention,
        "Não foi possível carregar os produtos e combos de ofertas. Tente novamente mais tarde.",
        buttonName: "Ok",
      );
      return;
    }

    await loadAlphabet();

    productListFull.clear();
    // Mapeia os produtos do SKU
    productListFull.addAll(
      globalParams
          .getProducts()!
          .produtos!
          .skus!
          .where((x) => x.isDemonstraGridPedido == true)
          .map((e) {
            List<OrdersProductsDiscountRangeModel>? discountRanges =
                e.faixasDesconto != null
                    ? e.faixasDesconto!
                        .map(
                          (x) => OrdersProductsDiscountRangeModel(
                            minQtdy: x.quantidadeMinima ?? 0,
                            maxQtdy: x.quantidadeMaxima,
                            comercialCondition: x.condicaoComercial,
                            baseDiscountPercentage:
                                x.condicaoComercial != null
                                    ? x
                                        .condicaoComercial!
                                        .percentualDescontoBase
                                    : 0,
                          ),
                        )
                        .toList()
                    : [];
            discountPercent = 0;
            discountMax = 0;
            discountMin = 0;
            exactDiscount = 0;
            roundedDiscount = 0;
            discount = 0;
            discountAvailable = 0;

            if (discountRanges.isNotEmpty) {
              discountPercent = calculateDiscountItem(discountRanges.first);
              discountMin = calculateDiscountMin(discountRanges.first);
              discountMax = calculateDiscountMax(discountRanges.first);
              exactDiscount = (e.preco! * discountPercent) / 100;
              roundedDiscount = double.parse(
                (exactDiscount + 0.005).toStringAsFixed(2),
              );
              discount = e.preco! - roundedDiscount;
              discountAvailable = discountRanges.first.getDiscountApply();
            }
            discountChange = discountPercent;
            discountTotal = discountPercent;
            return OrdersProductsListModel(
              productId: e.idProduto,
              productCode: e.ean,
              productCodeLabel: "EAN",
              paymentTermId: getPaymentTerm(
                globalParams.getProducts()!.produtosDistribuidores!,
                e.idProduto!,
              ),
              states: getProductStateEnum(e.idProduto),
              status: e.status,
              destaque: e.destaque,
              distributorId: getProductDistributorId(e.idProduto),
              isActive: globalParams.getProducts()!.produtosDistribuidores!.any(
                (d) => d.idProduto == e.idProduto,
              ),
              discount: discount,
              totalOrder: (e.preco! - (e.preco! * discountTotal) / 100),
              price: e.preco,
              priceOrder: e.preco,
              limitDate: e.menorDataVigencia,
              minQtdy: e.quantidadeMinima,
              maxQtdy: e.quantidadeEstoque,
              qtdy: 0,
              qtdyController: TextEditingController(text: "0"),
              focusNode: FocusNode(),
              focusNodeDiscountChange: FocusNode(),
              discountPercent: discountPercent,
              discountChange: discountChange,
              discountMax: discountMax,
              discountMin: discountMin,
              discountManager: discountRanges.first.getDiscountManager(),
              discountApply: discountAvailable,
              discountTotal: discountTotal,
              discountAdditional: 0,
              name: e.descricao,
              photo: e.caminhoFoto,
              emphasis: e.destaque,
              discountRange: discountRanges,
              discountRangeCurrent: discountRanges.first,
              stock: e.quantidadeEstoque,
              priceDistributor: e.precoDistribuidor ?? false,
              isEditDiscount: false,
              valueController: TextEditingController(
                text: discountPercent.formatarDouble(),
              ),
              discountChangeController: TextEditingController(text: "0"),
              filterIds:
                  e.filtrosPersonalizados?.map((x) => x.codigo!).toList(),
              metricaMdtr: e.metricaMdtr,
              taxSubstitution: e.substituicaoTributaria,
              showPriceWithSpecialST:
                  getParamOrderDiscount().exibePrecoComSTEspecial,
              showPriceWithRepST: getParamOrderDiscount().exibePrecoComSTRep,
              discountTaxSubstitution: 0,
              precoMaximo: e.precoMaximo,
              caixaEmbarque: e.caixaEmbarque,
              codigo: e.codigo,
              principioAtivo: e.principioAtivo,
            );
          }),
    );

    // Mapeia os produtos do produtoDUN
    if (globalParams.getProducts()!.produtosDUN != null) {
      productListFull.addAll(
        globalParams.getProducts()!.produtosDUN!.map((e) {
          final hasProduct = productListFull.any(
            (element) =>
                element.productId == e.idProduto &&
                element.productDunId == null,
          );
          OrdersProductsListModel? productBase =
              hasProduct
                  ? productListFull
                      .where(
                        (element) =>
                            element.productId == e.idProduto &&
                            element.productDunId == null,
                      )
                      .first
                  : null;

          return OrdersProductsListModel(
            productId: e.idProduto,
            productDunId: e.idProdutoDUN,
            productCode: e.dUN,
            status: e.status,
            destaque: e.destaque,
            productCodeLabel: "DUN",
            paymentTermId: getPaymentTerm(
              globalParams.getProducts()!.produtosDistribuidores!,
              e.idProduto!,
            ),
            price: productBase?.price,
            priceOrder: (productBase?.price ?? 0) * (e.quantidade ?? 1),
            totalOrder: discount * (e.quantidade ?? 1),
            quantidadeDUN: e.quantidade,
            priceDistributor: productBase?.priceDistributor,
            isActive: false,
            distributorId: getProductDistributorDunId(e.idProdutoDUN),
            states: getProductStateEnumDun(e.idProdutoDUN!),
            discount: discount,
            minQtdy: 0,
            maxQtdy: 0,
            qtdy: 0,
            qtdyController: TextEditingController(text: "0"),
            focusNode: FocusNode(),
            focusNodeDiscountChange: FocusNode(),
            discountPercent: productBase?.discountPercent,
            discountChange: productBase?.discountChange,
            discountManager: productBase?.discountManager,
            discountApply: productBase?.discountApply,
            discountTotal: productBase?.discountTotal,
            discountBase: productBase?.discountBase,
            discountMax: productBase?.discountMax,
            discountAdditional: productBase?.discountAdditional,
            discountMin: productBase?.discountMin,
            discountNegotiated: productBase?.discountNegotiated,
            discountNegotiation: productBase?.discountNegotiation,
            discountRep: productBase?.discountRep,
            name: e.apresentacao,
            photo: e.foto,
            emphasis: e.destaque,
            discountRange: productBase?.discountRange,
            discountRangeCurrent: productBase?.discountRangeCurrent,
            isEditDiscount: false,
            valueController: TextEditingController(
              text: discountPercent.formatarDouble(),
            ),
            discountChangeController: TextEditingController(text: "0"),
            filterIds: e.filtrosPersonalizados?.map((x) => x.codigo!).toList(),
            metricaMdtr: null,
            codigo: e.codigo,
            principioAtivo: e.principioAtivo,
            precoMaximo: e.precoMaximo,
          );
        }),
      );
    }

    if (isEditOrder == true) {
      for (var item in productListFull) {
        final productEdit = editItensOrder.firstWhereOrNull(
          (e) =>
              e.productId == item.productId &&
              e.productIdDun == item.productDunId,
        );

        if (productEdit != null) {
          item.qtdyController!.text = productEdit.qtdy.toString();
          item.discountAdditional = productEdit.discountApply;
          item.discountChangeController!.text =
              productEdit.discountApply.toString();
          item.qtdy = productEdit.qtdy;

          if (productEdit.isEditDiscount == true) {
            item.isEditDiscount = productEdit.isEditDiscount;
            item.valueController!.text = productEdit.discount.toString();
            item.discountTotal = productEdit.discount;
            item.discountChange = productEdit.discount;
          }
        }
      }

      if (globalParams.getProducts()!.produtos!.combosOferta != null &&
          globalParams.getProducts()!.produtos!.combosOferta!.isNotEmpty) {
        List<CombosOfertaModel> comboNewListEdit = [];
        for (var combo in globalParams.getProducts()!.produtos!.combosOferta!) {
          if (editItensComboOrder.any(
            (e) => e.offerComboId == combo.idComboOferta,
          )) {
            final productEdit =
                editItensComboOrder
                    .where((e) => e.offerComboId == combo.idComboOferta)
                    .first;
            combo.qtdy = productEdit.qtdy;

            comboNewListEdit.add(combo);
          }
        }
        globalParams.order.products!.produtos!.combosOferta = comboNewListEdit;
      }
    }

    for (var item in productListFull) {
      item.focusNode!.addListener(() async {
        if (item.focusNode!.hasFocus) {
          item.qtdyController!.selection = TextSelection(
            baseOffset: 0,
            extentOffset: item.qtdyController!.text.length,
          );
        } else {
          if (item.qtdyController!.text.isNotEmpty) {
            await setQtdyEdit(item, int.parse(item.qtdyController!.text));
          }
        }
      });

      item.focusNodeDiscountChange!.addListener(() async {
        if (item.focusNodeDiscountChange!.hasFocus) {
          item.discountChangeController!.selection = TextSelection(
            baseOffset: 0,
            extentOffset: item.discountChangeController!.text.length,
          );
        } else {
          if (item.discountManager != 0 || item.discountApply != 0) {
            if (item.discountChangeController!.text.isNotEmpty) {
              setDiscountAddRep(
                item,
                item.discountChangeController!.text,
                isEdit: true,
              );
            }
          }
        }
      });
    }

    for (var item in productListFull) {
      await calculateDiscountByItem(item);
      setFiltersItens(item);
    }
    productListFull.sort((a, b) => a.name!.compareTo(b.name!));
    productList = productListFull;
    productListFilteredByFilters = List.from(productListFull);

    if (!isEditOrder) {
      update();
      await showMixIdealMessage();
    } else {
      for (var item in productListFull) {
        setItemFilterResume(item, item.qtdy == 0);
        await calculateDiscountByItem(item);
      }
      updateFooter();
    }
  }

  void setFiltersItens(OrdersProductsListModel item) {
    item.filterIds ??= [];
    if (item.status != null && item.status!.isNotEmpty) {
      item.filterIds?.add(item.status?.toLowerCase() ?? "");
    }
    if (item.destaque == true) {
      item.filterIds?.add("destaque");
    }
    if (item.discountRangeCurrent?.comercialCondition?.idDescontoCupom != 0) {
      item.filterIds?.add("cupomDesconto");
    }
  }

  double calculateDiscountMaxRep(
    OrdersProductsDiscountRangeModel? variavelDiscount,
  ) {
    final typeOrder = globalParams.getTypeOrderId();
    if (typeOrder == TyperOrderEnum.especial) {
      return variavelDiscount.getDiscountMaxEsp();
    }
    return variavelDiscount.getDiscountMaxRep();
  }

  double calculateDiscountMax(
    OrdersProductsDiscountRangeModel? variavelDiscount,
  ) {
    final typeOrder = globalParams.getTypeOrderId();
    if (typeOrder == TyperOrderEnum.especial) {
      return variavelDiscount.getDiscountMaxEsp();
    }
    return variavelDiscount.getDiscountMax();
  }

  double calculateDiscountRange(
    OrdersProductsDiscountRangeModel? variavelDiscount,
  ) {
    final typeOrder = globalParams.getTypeOrderId();
    if (typeOrder == TyperOrderEnum.especial) {
      return variavelDiscount.getDiscountMaxEsp();
    }
    return variavelDiscount.getDiscountRange();
  }

  double calculateDiscountMin(
    OrdersProductsDiscountRangeModel? variavelDiscount,
  ) {
    final typeOrder = globalParams.getTypeOrderId();
    if (typeOrder == TyperOrderEnum.especial) {
      return variavelDiscount.getDiscountMinEsp();
    }
    return variavelDiscount.getDiscountMin();
  }

  double calculateDiscountItem(
    OrdersProductsDiscountRangeModel? variavelDiscount,
  ) {
    double discountItem = 0;
    final typeOrder = globalParams.getTypeOrderId();
    if (typeOrder == TyperOrderEnum.especial) {
      discountItem = 0;
    } else {
      if (variavelDiscount != null) {
        discountItem = variavelDiscount.baseDiscountPercentage ?? 0;
        if (typeOrder == TyperOrderEnum.rep ||
            typeOrder == TyperOrderEnum.padrao) {
          if (variavelDiscount.comercialCondition!.idDescontoCupom! > 0) {
            discountItem =
                variavelDiscount.comercialCondition!.percentualDescontoCupom ??
                0;
          } else {
            discountItem +=
                variavelDiscount
                    .comercialCondition!
                    .percentualDescontoNegociado ??
                0;
            discountItem +=
                variavelDiscount
                    .comercialCondition!
                    .percentualDescontoNegociacao1 ??
                0;
            discountItem +=
                variavelDiscount
                    .comercialCondition!
                    .percentualDescontoNegociacao2 ??
                0;
            discountItem +=
                variavelDiscount
                    .comercialCondition!
                    .percentualDescontoNegociacao3 ??
                0;
            discountItem +=
                variavelDiscount
                    .comercialCondition!
                    .percentualDescontoNegociacao4 ??
                0;
          }
        }
      }
    }
    if (discountItem > 99.99) discountItem = 99.99;
    return discountItem;
  }

  Future<void> showMixIdealMessage() async {
    if (globalParams.getCurrentStore()?.dataExtra?.offlineDateSync != null) {
      final offlineData = orderPaymentTypeController.offlineData;

      if (offlineData != null &&
          offlineData.synchronizationIdealMixResult?.isNotEmpty == true) {
        await showConfirmationDialog();
        return;
      }
      return;
    }

    // Verifique se o mix ideal está disponível
    if (await ordersIdealMixController.mixIdealIsAvailable()) {
      await showConfirmationDialog();
    }
  }

  void setSearchFilter(String? v) {
    searchFilter = v;

    if (v == null) {
      searchController.text = "";
      // Ao invés de resetar para todos os produtos, aplique os filtros personalizados ativos
      filterProducts();
    } else {
      if (productListFilteredByFilters.isEmpty) {
        productListFilteredByFilters = List.from(productListFull);
      }
      // Busca apenas dentro da lista já filtrada pelos filtros personalizados
      productList =
          productListFilteredByFilters
              .where(
                (element) =>
                    element.name!.containsInsesitive(searchFilter!) ||
                    element.productCode!.containsInsesitive(searchFilter!) ||
                    element.codigo?.containsInsesitive(searchFilter!) == true ||
                    element.principioAtivo?.containsInsesitive(searchFilter!) ==
                        true,
              )
              .toList();
    }
    update();
  }

  int? getProductDistributorId(int? productId) {
    final productDistributors =
        globalParams
            .getProducts()!
            .produtosDistribuidores!
            .where((d) => d.idProduto == productId)
            .toList();
    var distributors = globalParams.getParameters()!.idsDistribuidores;
    // Ordenando productDistributors pelo campo 'order'
    distributors!.sort((a, b) => a.ordem!.compareTo(b.ordem!));

    bool isAttendedByDistributor(int distributorIndex) =>
        productDistributors.any(
          (element) => element.idsDistribuidores!.contains(
            distributors[distributorIndex].idDistribuidor,
          ),
        );

    if (isAttendedByDistributor(0)) {
      return distributors[0].idDistribuidor;
    }

    for (var i = 1; i < distributors.length; i++) {
      if (isAttendedByDistributor(i)) {
        return distributors[i].idDistribuidor;
      }
    }
    return null;
  }

  int? getProductDistributorDunId(int? productDunId) {
    final productDistributors =
        globalParams
            .getProducts()!
            .produtosDUNDistribuidor!
            .where((d) => d.idProdutoDUN == productDunId)
            .toList();
    var distributors = globalParams.getParameters()!.idsDistribuidores;
    // Ordenando productDistributors pelo campo 'order'
    distributors!.sort((a, b) => a.ordem!.compareTo(b.ordem!));

    bool isAttendedByDistributor(int distributorIndex) =>
        productDistributors.any(
          (element) => element.idsDistribuidores!.contains(
            distributors[distributorIndex].idDistribuidor,
          ),
        );

    if (isAttendedByDistributor(0)) {
      return distributors[0].idDistribuidor;
    }

    for (var i = 1; i < distributors.length; i++) {
      if (isAttendedByDistributor(i)) {
        return distributors[i].idDistribuidor;
      }
    }
    return null;
  }

  ProductStateEnum getProductStateEnum(int? productId) {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "getProductStateEnum",
    );

    try {
      final productDistributors =
          globalParams
              .getProducts()!
              .produtosDistribuidores!
              .where((d) => d.idProduto == productId)
              .toList();
      var distributors = globalParams.getParameters()!.idsDistribuidores;
      // Ordenando productDistributors pelo campo 'order'
      distributors!.sort((a, b) => a.ordem!.compareTo(b.ordem!));

      bool isAttendedByDistributor(int distributorIndex) =>
          productDistributors.any(
            (element) => element.idsDistribuidores!.contains(
              distributors[distributorIndex].idDistribuidor,
            ),
          );

      if (isAttendedByDistributor(0)) {
        return ProductStateEnum.done;
      }

      for (var i = 1; i < distributors.length; i++) {
        if (isAttendedByDistributor(i)) {
          return ProductStateEnum.alert;
        }
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      log(e.toString());
      rethrow;
    } finally {
      leaveAction();
    }

    return ProductStateEnum.block;
  }

  ProductStateEnum getProductEspecialStateEnum(int? productId) {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "getProductEspecialStateEnum",
    );

    try {
      final productDistributors =
          productsTabloidSpecial!.produtosDistribuidores!
              .where((d) => d.idProduto == productId)
              .toList();
      var distributors = globalParams.getParametersEsp()!.distributorsIds;
      // Ordenando productDistributors pelo campo 'order'

      bool isAttendedByDistributor(int distributorIndex) =>
          productDistributors.any(
            (element) => element.idsDistribuidores!.contains(
              distributors![distributorIndex],
            ),
          );

      if (isAttendedByDistributor(0)) {
        return ProductStateEnum.done;
      }

      for (var i = 1; i < distributors!.length; i++) {
        if (isAttendedByDistributor(i)) {
          return ProductStateEnum.alert;
        }
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      log(e.toString());
      rethrow;
    } finally {
      leaveAction();
    }

    return ProductStateEnum.block;
  }

  int? getProductEspecialDistributor(int? productId) {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "getProductEspecialDistributor",
    );

    try {
      final productDistributors =
          productsTabloidSpecial!.produtosDistribuidores!
              .where((d) => d.idProduto == productId)
              .toList();

      var distributors = globalParams.getParametersEsp()!.distributorsIds;

      bool isAttendedByDistributor(int distributorIndex) =>
          productDistributors.any(
            (element) => element.idsDistribuidores!.contains(
              distributors![distributorIndex],
            ),
          );

      if (isAttendedByDistributor(0)) {
        return distributors![0];
      }

      for (var i = 1; i < distributors!.length; i++) {
        if (isAttendedByDistributor(i)) {
          return distributors[i];
        }
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      log(e.toString());
      rethrow;
    } finally {
      leaveAction();
    }

    return null;
  }

  ProductStateEnum getProductStateEnumDun(int productIdDun) {
    final productDistributors =
        globalParams
            .getProducts()!
            .produtosDUNDistribuidor!
            .where((d) => d.idProdutoDUN == productIdDun)
            .toList();
    var distributors = globalParams.getParameters()!.idsDistribuidores;
    // Ordenando productDistributors pelo campo 'order'
    distributors!.sort((a, b) => a.ordem!.compareTo(b.ordem!));

    bool isAttendedByDistributor(int distributorIndex) =>
        productDistributors.any(
          (element) => element.idsDistribuidores!.contains(
            distributors[distributorIndex].idDistribuidor,
          ),
        );

    if (isAttendedByDistributor(0)) {
      return ProductStateEnum.done;
    }

    for (var i = 1; i < distributors.length; i++) {
      if (isAttendedByDistributor(i)) {
        return ProductStateEnum.alert;
      }
    }

    return ProductStateEnum.block;
  }

  Future<void> showConfirmationDialog() async {
    await Dialogs.confirm(
      AppStrings.orderConfirmTitle1,
      AppStrings.orderConfirmMessage1,
      onPressedOk: () async {
        GetC.close();
        await ordersIdealMixController.getData();
      },
    );
  }

  void showFilters() {
    showDialog(
      context: Get.context!,
      builder: (BuildContext context) {
        return AlertDialog(
          title: LabelWidget(
            title: 'Selecione os filtros',
            fontSize: 14.sp,
            fontWeight: FontWeight.bold,
          ),
          content: GetBuilder<OrdersController>(
            builder: (ctrl) {
              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children:
                      filterDefaults.map((e) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            LabelWidget(
                              title: e.descricao ?? "",
                              fontSize: 15.sp,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            ...e.filtrosPersonalizados?.map((filtro) {
                                  return CheckboxListTile(
                                    value:
                                        filtro
                                            .ativo, // Defina o valor com base no estado do item
                                    onChanged: (value) {
                                      setFilterType(filtro);
                                    },
                                    dense: true,
                                    activeColor:
                                        themesController.getPrimaryColor(),
                                    title: Text(filtro.descricao ?? ""),
                                    controlAffinity:
                                        ListTileControlAffinity.leading,
                                  );
                                }).toList() ??
                                [],
                          ],
                        );
                      }).toList(),
                ),
              );
            },
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                GetC.close(); // Fecha o diálogo sem fazer nada.
              },
              child: const Text('Cancelar'),
            ),
            TextButton(
              onPressed: () async {
                await filterProducts();
                GetC.close();
              },
              child: const Text('Ok'),
            ),
          ],
        );
      },
    );
  }

  Future<void> validateRepDiscount(
    OrdersProductsListModel item, {
    bool isEdit = false,
  }) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "validateRepDiscount",
    );

    OrdersProductsDiscountRangeModel? range;
    try {
      if (item.discountRange!.isNotEmpty) {
        subAction.reportValue("validateRepDiscount", item);

        if (!item.discountRange!.any(
          (element) =>
              element.minQtdy! <= item.qtdy! && element.maxQtdy! >= item.qtdy!,
        )) {
          range = item.discountRangeCurrent;
        } else {
          range =
              item.discountRange!
                  .where(
                    (element) =>
                        element.minQtdy! <= item.qtdy! &&
                        element.maxQtdy! >= item.qtdy!,
                  )
                  .first;
        }

        double discountPercent = calculateDiscountItem(range);
        double discountMaxRep = calculateDiscountMax(range) + discountPercent;
        double exactDiscount = (item.price! * discountPercent) / 100;
        double roundedDiscount = double.parse(
          (exactDiscount + 0.005).toStringAsFixed(2),
        );
        double discount = item.price! - roundedDiscount;
        item.discount = discount;
        item.discountPercent = discountPercent;

        //item.valueController!.text = discountPercent.toString();
        if (item.discountRangeCurrent != range) {
          item.discountRangeCurrent = range;
          item.discountChange = item.discountRangeCurrent!.discountMin!;
          if (!isEdit) {
            item.discountChangeController!.text =
                item.discountRangeCurrent!.discountMin!.formatarDouble();
          }
        }
        if (globalParams.getTypeOrderId() == TyperOrderEnum.rep) {
          if (!isEdit) {
            item.discountChangeController!.text =
                item.discountAdditional!.formatarDouble();
          }
          if ((item.discountAdditional! + item.discountChange!) >
              discountMaxRep) {
            item.discountAdditional = discountMaxRep - item.discountChange!;
            if (!isEdit) {
              item.discountChangeController!.text =
                  item.discountAdditional!.formatarDouble();
            }
            item.discountTotal = discountMaxRep;
            await Dialogs.info(
              AppStrings.attention,
              AppStrings.discountMax1,
              buttonName: "Entendi".toUpperCase(),
            );
          }
          exactDiscount = (item.price! * item.discountTotal!) / 100;
          roundedDiscount = double.parse(
            (exactDiscount + 0.005).toStringAsFixed(2),
          );
          discount = item.price! - roundedDiscount;
          item.discount = discount;
        }
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> calculateDiscountByItem(
    OrdersProductsListModel item, {
    bool isMixIdeal = false,
  }) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "calculateDiscountByItem",
    );

    OrdersProductsDiscountRangeModel? range;

    try {
      if (item.discountRange != null && item.discountRange!.isNotEmpty) {
        if (!item.discountRange!.any(
          (element) =>
              element.minQtdy! <= item.qtdy! && element.maxQtdy! >= item.qtdy!,
        )) {
          range = item.discountRangeCurrent;
        } else {
          range =
              item.discountRange!
                  .where(
                    (element) =>
                        element.minQtdy! <= item.qtdy! &&
                        element.maxQtdy! >= item.qtdy!,
                  )
                  .first;
        }

        double discountPercent = calculateDiscountItem(range);
        double discountMin = calculateDiscountMin(range);
        double discountMax = calculateDiscountMax(range);

        double exactDiscount = (item.price! * discountPercent) / 100;
        double roundedDiscount = double.parse(
          (exactDiscount + 0.005).toStringAsFixed(2),
        );
        double discount = item.price! - roundedDiscount;
        item.discount = roundedDiscount;
        item.discountPercent = discountPercent;

        //item.valueController!.text = discountPercent.toString();
        if (item.discountRangeCurrent != range) {
          item.discountRangeCurrent = range;
          item.discountChange = item.discountRangeCurrent!.discountMax ?? 0;
          item.discountChangeController!.text =
              item.discountChange!.formatarDouble();
        }

        item.discountMin = discountMin;
        item.discountMax = discountMax;
        if (globalParams.getTypeOrderId() == TyperOrderEnum.especial) {
          discountPercent = item.discountChange!;
          exactDiscount = (item.price! * discountPercent) / 100;
          roundedDiscount = double.parse(
            (exactDiscount + 0.005).toStringAsFixed(2),
          );
          discount = item.price! - roundedDiscount;
          item.discount = discount;
          item.discountTotal = discountPercent;
        } else if (globalParams.getTypeOrderId() == TyperOrderEnum.rep) {
          if (item.isEditDiscount == true) {
            discountPercent = item.discountChange ?? discountPercent;
          }
          exactDiscount = (item.price! * item.discountTotal!) / 100;
          roundedDiscount = double.parse(
            (exactDiscount + 0.005).toStringAsFixed(2),
          );
          discount = item.price! - roundedDiscount;
          item.discount = discount;
          if (!isMixIdeal) {
            item.discountTotal = discountPercent + item.discountAdditional!;
          }
        } else {
          item.discountTotal = discountPercent;
        }

        item.qtdyController!.text = item.qtdy.toString();
        if (item.productDunId != null && item.quantidadeDUN != null) {
          item.qtdyReal = item.qtdy! * item.quantidadeDUN!;
        } else {
          item.qtdyReal = item.qtdy;
        }
        if (item.productDunId != null && item.quantidadeDUN != null) {
          if (item.priceDistributor == true) {
            item.priceOrder = item.price! * (item.qtdy! > 0 ? item.qtdy! : 1);
          } else {
            item.priceOrder =
                item.price! *
                (item.qtdy! > 0 ? item.qtdy! : 1) *
                item.quantidadeDUN!;
          }
          item.totalOrder =
              item.priceOrder! - (item.priceOrder! * item.discountTotal!) / 100;
        } else {
          item.priceOrder = item.price! * (item.qtdy! > 0 ? item.qtdy! : 1);
          item.totalOrder =
              item.priceOrder! - (item.priceOrder! * item.discountTotal!) / 100;
        }

        if (item.taxSubstitution != null) {
          item.discountTaxSubstitution =
              item.qtdy! >= 0
                  ? (item.totalOrder! +
                      ((item.totalOrder! * item.taxSubstitution!) / 100))
                  : 0;
        } else {
          item.discountTaxSubstitution = 0;
        }
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  void setItemFilterResume(OrdersProductsListModel item, bool isRemove) {
    if (isRemove) {
      item.filterIds?.removeWhere((element) => element == "resume");
    } else {
      item.filterIds?.add("resume");
    }
  }

  Future<void> setQtdyZero(OrdersProductsListModel item) async {
    item.qtdy = 1;

    setItemFilterResume(item, item.qtdy == 0);

    await calculateDiscountByItem(item);

    item.qtdy = 0;
    if (item.productDunId != null && item.quantidadeDUN != null) {
      item.qtdyReal = item.qtdy! * item.quantidadeDUN!;
    } else {
      item.qtdyReal = item.qtdy;
    }
    item.qtdyController!.text = "0";
    updateFooter();
  }

  Future<void> setQtdyByLoading(OrdersProductsListModel item, int value) async {
    item.qtdy = value;

    setItemFilterResume(item, item.qtdy == 0);

    await calculateDiscountByItem(item);
  }

  Future<void> setQtdyEdit(OrdersProductsListModel item, int value) async {
    item.qtdy = value;

    setItemFilterResume(item, item.qtdy == 0);

    await calculateDiscountByItem(item);

    updateFooter();
    await OrdersControllerPartLocal.saveOrderLocal(this);
  }

  Future<void> setQtdyUp(OrdersProductsListModel item) async {
    item.qtdy = item.qtdy! + 1;
    setItemFilterResume(item, item.qtdy == 0);
    await calculateDiscountByItem(item);

    updateFooter();
    await OrdersControllerPartLocal.saveOrderLocal(this);
  }

  Future<void> setQtdyDown(OrdersProductsListModel item) async {
    item.qtdy = item.qtdy! - 1;
    if (item.qtdy! < 0) item.qtdy = 0;
    setItemFilterResume(item, item.qtdy == 0);
    await calculateDiscountByItem(item);

    item.qtdyController!.text = item.qtdy.toString();
    if (item.productDunId != null && item.quantidadeDUN != null) {
      item.qtdyReal = item.qtdy! * item.quantidadeDUN!;
    } else {
      item.qtdyReal = item.qtdy;
    }
    if (item.productDunId != null && item.quantidadeDUN != null) {
      if (item.priceDistributor == true) {
        item.priceOrder = item.price! * (item.qtdy! == 0 ? 1 : item.qtdy!);
      } else {
        item.priceOrder =
            item.price! *
            (item.qtdy! == 0 ? 1 : item.qtdy!) *
            item.quantidadeDUN!;
      }
      item.totalOrder =
          item.priceOrder! - (item.priceOrder! * item.discountTotal!) / 100;
    } else {
      item.priceOrder = item.price! * (item.qtdy! == 0 ? 1 : item.qtdy!);
      item.totalOrder =
          item.priceOrder! - (item.priceOrder! * item.discountTotal!) / 100;
    }
    updateFooter();
    await OrdersControllerPartLocal.saveOrderLocal(this);
  }

  void updateFooter() {
    footer.totalGross = productListFull
        .where((element) => element.qtdy! > 0)
        .map((e) => e.priceOrder ?? 0)
        .fold(0, (a, b) => a! + b);
    footer.totalNet = productListFull
        .where((element) => element.qtdy! > 0)
        .map((e) => e.totalOrder ?? 0)
        .fold(0, (a, b) => a! + b);

    footer.totalUnits = productListFull
        .where((element) => element.qtdy! > 0)
        .map((e) => e.qtdy ?? 0)
        .fold(0, (a, b) => a! + b);

    footer.qtyReal = productListFull
        .where((element) => element.qtdy! > 0)
        .map((e) => e.qtdyReal ?? 0)
        .fold(0, (a, b) => a! + b);

    footer.totalApresentation =
        productListFull.where((element) => element.qtdy! > 0).length;

    footer.discount =
        footer.totalGross != 0
            ? (((footer.totalGross! - footer.totalNet!) / footer.totalGross!) *
                100)
            : 0;

    update();
  }

  Future<List<ProductFilterDefaultModel>> loadFiltersDefault() async {
    final jsonString = await rootBundle.loadString(
      'assets/data/product_filters.json',
    );
    final jsonData = json.decode(jsonString);

    List<ProductFilterDefaultModel> filtersList = [];
    for (var item in jsonData) {
      filtersList.add(ProductFilterDefaultModel.fromJson(item));
    }

    return filtersList;
  }

  Future<List<ProductFilterDefaultModel>> loadFiltersCustom() async {
    generalParameterizationController.filterCustom!
        .map(
          (e) => e.filtrosPersonalizados!.map((x) => x.ativo = false).toList(),
        )
        .toList();
    return generalParameterizationController.filterCustom!;
  }

  Future<List<ProductFilterDefaultModel>> loadGeneralParameters() async {
    List<ProductFilterDefaultModel> filtersList = [];
    final res = generalParameterizationController.parameter!;
    if (res.parametrizacaoExibirFiltrosPersonalizadosPedido != null &&
        !res.parametrizacaoExibirFiltrosPersonalizadosPedido!.desabilitar!) {
      if ((res.parametrizacaoExibirFiltrosPersonalizados!.pedidoEspecial! &&
              isOrderEspecial()) ||
          (res.parametrizacaoExibirFiltrosPersonalizados!.pedidoPadrao! &&
              isOrderPad()) ||
          (res.parametrizacaoExibirFiltrosPersonalizados!.pedidoREP! &&
              isOrderRep())) {
        if (res
            .parametrizacaoExibirFiltrosPersonalizadosPedido!
            .ocultarPadrao!) {
          filtersList.addAll(await loadFiltersCustom());
        } else {
          filtersList.addAll(await loadFiltersDefault());
          filtersList.addAll(await loadFiltersCustom());
        }
      } else {
        filtersList.addAll(await loadFiltersDefault());
      }
    } else {
      filtersList.addAll(await loadFiltersDefault());
    }
    return filtersList;
  }

  bool isOrderPad() {
    return globalParams.order.typeOrderStr == TyperOrderStringEnum.padrao;
  }

  bool isOrderRep() {
    return globalParams.order.typeOrderStr == TyperOrderStringEnum.rep;
  }

  bool isOrderEspecial() {
    return globalParams.order.typeOrderStr == TyperOrderStringEnum.especial;
  }

  bool isShowStock() {
    final param = generalParameterizationController.parameter!;
    return param.parametrizacaoHabilitaEstoque!;
  }

  void setFilterType(FiltrosPersonalizados item) {
    for (var filterGroup in filterDefaults) {
      if (filterGroup.idCategoriaFiltrosPersonalizados ==
          item.idCategoriaFiltroPersonalizado) {
        // Desmarcar todos os outros filtros ativos na mesma categoria
        for (var filter in filterGroup.filtrosPersonalizados ?? []) {
          if (filter.idFiltroPersonalizado != item.idFiltroPersonalizado &&
              filter.ativo == true) {
            filter.ativo = false;
          }
        }
      }
    }

    item.ativo = !item.ativo!;

    setFilterSelectedString();
    update();
  }

  Future<void> filterProducts() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "filterProducts",
    );
    if (productListFull.isEmpty) {
      return;
    }
    productListFilteredByFilters = List.from(productListFull);
    try {
      if (filterIds.isNotEmpty) {
        productListFilteredByFilters =
            productListFilteredByFilters
                .where(
                  (element) =>
                      element.filterIds != null &&
                      filterIds.every((id) => element.filterIds!.contains(id)),
                )
                .toList();
      }
      // Aplique o filtro de busca por nome, se houver
      if (searchFilter != null && searchFilter!.isNotEmpty) {
        productList =
            productListFilteredByFilters
                .where(
                  (element) =>
                      element.name!.containsInsesitive(searchFilter!) ||
                      element.productCode!.containsInsesitive(searchFilter!) ||
                      element.codigo?.containsInsesitive(searchFilter!) ==
                          true ||
                      element.principioAtivo?.containsInsesitive(
                            searchFilter!,
                          ) ==
                          true,
                )
                .toList();
      } else {
        productList = List.from(productListFilteredByFilters);
      }
      update();
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      SnackbarCustom.snackbarError(e.toString());
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> filterProductsByLetter(String letter) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "filterProductsByLetter",
    );

    productList = List.from(productListFull);

    try {
      if (selectedLetter != letter) {
        selectedLetter = letter;
        productList =
            productList
                .where(
                  (element) => element.name!.toUpperCase().startsWith(letter),
                )
                .toList();

        subAction.reportValue("productList", productList);
      } else {
        selectedLetter = null;
      }
      updateFooter();
    } catch (e, s) {
      SnackbarCustom.snackbarError(e.toString());
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  void setFilterSelectedString() {
    filterSelectedString = "";
    filterIds = [];
    filterDefaults.map((e) {
      e.filtrosPersonalizados!.where((element) => element.ativo == true).map((
        x,
      ) {
        filterSelectedString =
            filterSelectedString.isEmpty
                ? x.descricao!
                : "$filterSelectedString, ${x.descricao}";

        filterIds.add(x.codigo!);
      }).toList();
    }).toList();
  }

  Future<void> setDiscountRep(
    OrdersProductsListModel item,
    String? value,
  ) async {
    if (value != null && value.isNotEmpty) {
      try {
        final params =
            generalParameterizationController
                .generalSettingsOrderDiscountRegistration!;

        double discountValue = double.parse(value.replaceAll(',', '.'));
        if (discountValue < item.discountMin! &&
            params.limitarReducaoDescontoBase!) {
          item.valueController!.text = item.discountPercent!.formatarDouble();
          item.discountChange = item.discountPercent!;
          await Dialogs.info(
            AppStrings.attention,
            AppStrings.discountEditMin,
            buttonName: "Entendi".toUpperCase(),
          );
        } else if (discountValue > item.discountPercent!) {
          item.valueController!.text = item.discountPercent!.formatarDouble();
          item.discountChange = item.discountPercent!;
          await Dialogs.info(
            AppStrings.attention,
            AppStrings.discountEditMax,
            buttonName: "Entendi".toUpperCase(),
          );
        } else {
          item.discountChange = discountValue;
        }
        // ignore: empty_catches
      } on FormatException {}
    }
    item.discountTotal = item.discountChange;
    await calculateDiscountByItem(item);
    updateFooter();
  }

  Future<void> setDiscountAddRep(
    OrdersProductsListModel item,
    String? value, {
    bool? isEdit = false,
  }) async {
    if (value != null && value.isNotEmpty) {
      try {
        if (item.discountChangeController!.text.contains(".")) {
          item.discountChangeController!.text = item
              .discountChangeController!
              .text
              .replaceAll('.', ',');
        }

        // Verifica se o valor é um número válido
        if (RegExp(r'^\d*\.?\d*$').hasMatch(value.replaceAll(',', '.'))) {
          double newValue = double.parse(value.replaceAll(',', '.'));

          item.discountAdditional = newValue;
          item.discountTotal = newValue + item.discountChange!;
        } else {
          item.discountChangeController!.text = cleanedText(
            item.discountChangeController!.text,
          );
        }

        // ignore: empty_catches
      } on FormatException {}
    }
    await validateRepDiscount(item, isEdit: isEdit ?? false);
    await calculateDiscountByItem(item);
    updateFooter();
    await OrdersControllerPartLocal.saveOrderLocal(this);
  }

  Future<void> setDiscountAddEsp(
    OrdersProductsListModel item,
    String? value,
  ) async {
    if (value != null && value.isNotEmpty) {
      try {
        final params =
            generalParameterizationController
                .generalSettingsOrderDiscountRegistration!;

        if (item.discountChangeController!.text.contains(".")) {
          item.discountChangeController!.text = item
              .discountChangeController!
              .text
              .replaceAll('.', ',');
        }

        // Verifica se o valor é um número válido
        if (RegExp(r'^\d*\.?\d*$').hasMatch(value.replaceAll(',', '.'))) {
          double discountChange = double.parse(value.replaceAll(',', '.'));
          if (item.discountRangeCurrent != null &&
              item.discountRangeCurrent!.discountMin! > discountChange &&
              params.limitarReducaoDescontoMinimoEspecial!) {
            discountChange = item.discountRangeCurrent!.discountMin!;
            item.discountChangeController!.text =
                discountChange.formatarDouble();
            item.discountChange = discountChange;
            await Dialogs.info(
              AppStrings.attention,
              AppStrings.discountMin1,
              buttonName: "Entendi".toUpperCase(),
            );
          } else if (item.discountRangeCurrent != null &&
              item.discountRangeCurrent!.discountMax! < discountChange) {
            discountChange = item.discountRangeCurrent!.discountMax!;
            item.discountChangeController!.text =
                discountChange.formatarDouble();
            item.discountChange = discountChange;
            await Dialogs.info(
              AppStrings.attention,
              AppStrings.discountMax1,
              buttonName: "Entendi".toUpperCase(),
            );
          } else {
            item.discountChange = discountChange;
          }
        } else {
          item.discountChangeController!.text = cleanedText(
            item.discountChangeController!.text,
          );
        }
      } on FormatException {
        // Ignora a exceção de formatação
      }
    }
    await calculateDiscountByItem(item);
    updateFooter();
    await OrdersControllerPartLocal.saveOrderLocal(this);
  }

  String cleanedText(String value) {
    String newText = value.replaceAll(RegExp(r'[^0-9,]'), '');
    if (newText.contains(',')) {
      List<String> parts = newText.split(',');
      if (parts.length > 2) {
        newText = '${parts[0]},${parts.sublist(1).join('')}';
      }
    }
    return newText;
  }

  Future<void> setEnableDiscountRep(OrdersProductsListModel item) async {
    item.isEditDiscount = !item.isEditDiscount!;

    if (!item.isEditDiscount!) {
      await Dialogs.confirm(
        AppStrings.confirmation,
        AppStrings.discountDiscart,
        buttonNameOk: "Ok".toUpperCase(),
        buttonNameCancel: "Cancelar".toUpperCase(),
        onPressedCancel: () async {
          item.isEditDiscount = true;

          await recalculePrices(item);
          GetC.close();
        },
        onPressedOk: () async {
          item.discountChange = item.discountPercent;
          item.discountTotal = item.discountChange;
          item.valueController!.text = item.discountChange!.formatarDouble();
          await recalculePrices(item);
          GetC.close();
        },
      );
    } else {
      item.discountAdditional = 0;
      item.discountChangeController!.text = "0";
      item.discountTotal = item.discountChange;
      await recalculePrices(item);
    }
  }

  Future<void> recalculePrices(OrdersProductsListModel item) async {
    await calculateDiscountByItem(item);
    updateFooter();
  }

  Future<bool> validateMinimumQuantity() async {
    List<OrdersProductsListModel> invalidItems = [];

    // Filtra os produtos com quantidade maior que 0
    final shoppingCartItems =
        productListFull.where((element) => element.qtdy! > 0).toList();

    for (var product in shoppingCartItems) {
      if (product.minQtdy! > 0 && product.qtdy! < product.minQtdy!) {
        invalidItems.add(product);
      }
    }

    if (invalidItems.isNotEmpty) {
      String errorMessage =
          "Quantidade é menor que a quantidade mínima para o(s) produto(s):\n\n";
      for (var product in invalidItems) {
        errorMessage +=
            "- ${product.name} - quantidade mínima: ${product.minQtdy} - quantidade informada: ${product.qtdy}\n";
      }

      await Dialogs.info("Atenção", errorMessage, buttonName: "Ok");
      return false;
    }

    return true;
  }

  Future<void> advance() async {
    return trace('advance', () async {
      var (leaveAction, subAction) = dynatraceAction.subActionReport("advance");
      try {
        if (!await validateMinimumQuantity()) {
          return;
        }
        ordersResumeController.cartProductsList =
            productListFull.where((element) => element.qtdy! > 0).toList();

        appLog(
          "Produtos selecionados",
          data: {"products": ordersResumeController.cartProductsList},
        );

        if (globalParams.getTypeOrderId() == TyperOrderEnum.especial) {
          final minOrderValue =
              globalParams
                  .getCurrentDistributor()!
                  .distribuidor!
                  .valorMinimoDePedido!;
          if (footer.totalNet! < minOrderValue &&
              productsTabloid!.combosOferta!.isEmpty) {
            appLogWithDynatrace(
              subAction,
              AppStrings.orderSpecialMinValueMessage(
                minOrderValue.formatReal(),
                footer.totalNet!.formatReal(),
              ),
            );

            await Dialogs.info(
              AppStrings.attention,
              AppStrings.orderSpecialMinValueMessage(
                minOrderValue.formatReal(),
                footer.totalNet!.formatReal(),
              ),
              buttonName: "Entendi".toUpperCase(),
            );
            return;
          }
          isItemMinRequired = false;
          isItemMaxRequired = false;
          if (globalParams.getTabloidSelected() != null &&
              globalParams.getTabloidSelected()!.itemMinimumQuantity != null) {
            if (globalParams.getTabloidSelected()!.itemMinimumQuantity! >
                footer.totalUnits!) {
              await Dialogs.info(
                AppStrings.attention,
                "O *total* de unidades que você digitou é *menor que o mínimo* permitido para esta oferta especial, por favor, ajuste as quantidades para prosseguir!",
                buttonName: "Prosseguir".toUpperCase(),
              );
              isItemMinRequired = true;
              updateFooter();
              return;
            }
          }
          if (globalParams.getTabloidSelected() != null &&
              globalParams.getTabloidSelected()!.itemMaximumQuantity != null) {
            if (globalParams.getTabloidSelected()!.itemMaximumQuantity! <
                footer.totalUnits!) {
              await Dialogs.info(
                AppStrings.attention,
                "O *total* de unidades que você digitou é *maior que o máximo* permitido para esta oferta especial, por favor, ajuste as quantidades para prosseguir!",
                buttonName: "Prosseguir".toUpperCase(),
              );
              isItemMaxRequired = true;
              updateFooter();
              return;
            }
          }

          final validStateProducts = await validateDiscountByItemSpecial();
          if (productsTabloid!.combosOferta!.isNotEmpty) {
            if (ordersResumeController.cartProductsList.isNotEmpty &&
                !validStateProducts) {
              return;
            }
            appLogWithDynatrace(subAction, "Redirecionando para o combos");
            Get.toNamed(RoutesPath.ordersCombo)?.then((value) {
              updateFooter();
            });
          } else {
            if (ordersResumeController.cartProductsList.isEmpty) {
              await Dialogs.info(
                AppStrings.attention,
                AppStrings.orderNoProducts,
                buttonName: "Entendi".toUpperCase(),
              );
              return;
            }
            if (ordersResumeController.cartProductsList.isNotEmpty &&
                !validStateProducts) {
              return;
            }
            appLogWithDynatrace(
              subAction,
              "Redirecionando para o resumo de pedidos",
            );
            await ordersResumeController.initialize();
            Get.toNamed(RoutesPath.ordersResume)?.then((value) {
              updateFooter();
            });
          }
        } else if (globalParams
            .getProducts()!
            .produtos!
            .combosOferta!
            .isEmpty) {
          if (ordersResumeController.cartProductsList.isEmpty) {
            await Dialogs.info(
              AppStrings.attention,
              AppStrings.orderNoProducts,
              buttonName: "Entendi".toUpperCase(),
            );
            return;
          }
          appLogWithDynatrace(
            subAction,
            "Redirecionando para o resumo de pedidos",
          );
          await ordersResumeController.initialize();
          Get.toNamed(RoutesPath.ordersResume)?.then((value) {
            updateFooter();
          });
        } else {
          appLogWithDynatrace(subAction, "Redirecionando para o combos");
          Get.toNamed(RoutesPath.ordersCombo)?.then((value) {
            updateFooter();
          });
        }
      } catch (e, s) {
        SnackbarCustom.snackbarError(e.toString());
        appErrorWithDynatrace(subAction, e, s);
        rethrow;
      } finally {
        leaveAction();
      }
    });
  }

  void showProductBottomSheet(OrdersProductsListModel item) {
    showModalBottomSheet(
      context: Get.context!,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return ProductDetailsWidget(
          product: ProductDetailModel(
            name: item.name!,
            code: item.productCode!,
            codeLabel: item.productCodeLabel!,
            photo: item.photo ?? "",
            qtyMin: item.minQtdy ?? 0,
            qtyReal: item.qtdy,
            discountRep: item.discountRangeCurrent.getDiscountRep(),
            discountApply: item.discountAdditional ?? item.discountChange,
            discountBase: item.discountRangeCurrent.getDiscountBase(),
            discountManager: item.discountRangeCurrent.getDiscountManager(),
            discountNegotiated:
                item.discountRangeCurrent.getDiscountNegotiated(),
            discountNegotiation:
                item.discountRangeCurrent.getDiscountNegotiation(),
            discountRange: item.getDiscountRange(),
            priceDistributor: item.priceDistributor == true ? "Sim" : "Não",
            stock: item.stock,
            metricaMdtr: item.metricaMdtr,
          ),
        );
      },
    );
  }

  Future<void> mixIdealRefreshCart(List<MixIdealProdutosModel> items) async {
    final descontoCumulativo =
        ordersIdealMixController.dataList?.descontoCumulativo ?? false;
    for (var i in items) {
      if (productListFull.any((p) => p.productId == i.idProduto)) {
        var item = productListFull.firstWhere(
          (p) => p.productId == i.idProduto,
        );
        item.qtdy = i.qtdySelected;

        if (descontoCumulativo) {
          // Soma o desconto do mix ao desconto já existente
          final descontoAtual = item.discountChange ?? 0;
          final novoDesconto = descontoAtual + i.desconto!;
          item.discountChange = novoDesconto;
          item.discountTotal = novoDesconto;
          item.discountChangeController?.text = novoDesconto.toStringAsFixed(2);
        } else {
          // Substitui pelo desconto do mix
          item.discountChange = i.desconto;
          item.discountTotal = i.desconto;
          item.discountChangeController?.text = i.desconto!.toStringAsFixed(2);
        }
        item.isMixIdealDiscount = true;

        setItemFilterResume(item, item.qtdy == 0);
        await calculateDiscountByItem(item, isMixIdeal: true);
      }
    }
    updateFooter();
  }

  Future<void> loadInfoEditOrder(String orderId) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "loadInfoEditOrder",
    );

    final loading = PlkLoading();
    try {
      subAction.reportEvent("orderId: $orderId");

      if (syncOrders.any((element) => element.transactionKey == orderId)) {
        loading.show(title: AppStrings.load);
        //recuperar os dados do pedido
        syncOrderEdit =
            syncOrders
                .where((element) => element.transactionKey == orderId)
                .first;

        if (syncOrderEdit!.parameters?.typeOrderId == TyperOrderEnum.especial) {
          await tabloidUpdateData();
          globalParams.order.setParametersEsp(
            syncOrderEdit!.parameters!.paymentTypeParametersSpecial,
          );

          globalParams.order.setCurrentDistributor(
            syncOrderEdit!.parameters!.distributorCurrent,
          );
          loading.hide();
          await getProductsEspByOrder(syncOrderEdit!.parameters!);
        } else {
          await storeParametersController.getDataByStoreIdAndType(
            storeId: syncOrderEdit!.parameters!.paymentTypeParameters!.pdvId!,
            typeOrder: getTypeOrderString(
              syncOrderEdit!.parameters!.typeOrderId!,
            ),
            isOffline:
                globalParams.getCurrentStore()?.dataExtra?.offlineDateSync !=
                null,
          );

          final updateParameter =
              storeParametersController.dataList.firstOrNull;
          if (updateParameter != null) {
            syncOrderEdit!
                    .parameters!
                    .paymentTypeParameters!
                    .condicaoComercialBaseId =
                updateParameter.parametrizacao!.condicaoComercialBase!.id!;
            syncOrderEdit!
                .parameters!
                .paymentTypeParameters!
                .valorMinimoDePedido = updateParameter
                    .parametrizacao!
                    .condicaoComercialBase!
                    .valorMinimoDePedido!;

            globalParams.order.setDeadlinePaymentList(
              updateParameter.parametrizacao?.prazoPagamento,
            );
          }

          loading.hide();
          globalParams.order.setParameters(
            syncOrderEdit!.parameters!.paymentTypeParameters,
          );
          await getProductsByOrder(syncOrderEdit!.parameters!);
        }
      } else {
        GetC.close();
      }
    } catch (e, s) {
      loading.hide();
      Dialogs.info(
        AppStrings.attention,
        e.toString().replaceAll("Exception: ", ""),
        buttonOnPressed: () {
          Get.back();
          Get.until((route) => Get.currentRoute == RoutesPath.reportOrders);
        },
      );
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  String getTypeOrderString(int? typeOrderId) {
    switch (typeOrderId) {
      case 4:
        return TyperOrderStringEnum.rep;
      case 2:
        return TyperOrderStringEnum.especial;
      case 1:
      default:
        return TyperOrderStringEnum.padrao;
    }
  }

  Future<void> getTabloidProductsSpecial() async {
    final selected = syncOrderEdit!.parameters!.paymentTypeParametersSpecial;

    orderPaymentTypeController = Get.find<OrderPaymentTypeController>();

    ordersTabloidController.getTabloidInfo(
      tablodId: selected!.tabloidId!,
      deadlinePayments: selected.deadlinePayment!,
    );

    var modelDistribuidtors = GetMixSpecialProductsRequest(
      idPrazoPagamento: selected.paymentTermId,
      idsDistribuidores: selected.distributorsIds!.map((e) => e).toList(),
      idsProdutosDUN: orderPaymentTypeController.getProductIdsEsp(true),
      idsProdutos: orderPaymentTypeController.getProductIdsEsp(false),
    );
    final resultDistributors = await ordersApi.getTabloidProductsSpecial(
      model: modelDistribuidtors,
    );
    if (resultDistributors.error != null) {
      SnackbarCustom.snackbarError(
        resultDistributors.error!.message ??
            "Ocorreu um erro ao buscar os produtos especiais",
      );

      return;
    } else {
      productsTabloidSpecial = resultDistributors.data!;
    }
  }

  Future<void> tabloidUpdateData() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "tabloidUpdateData",
    );

    try {
      final result = await ordersApi.getTabloidUpdate(
        tablodId:
            syncOrderEdit!.parameters!.paymentTypeParametersSpecial!.tabloidId!,
        storeId:
            syncOrderEdit!.parameters!.paymentTypeParametersSpecial!.storeId!,
      );
      if (result.data != null) {
        syncOrderEdit!.parameters!.paymentTypeParametersSpecial!.tabloidId =
            result.data!.idTabloide!;
      } else {
        if (result.error!.data is ResultErrorModel) {
          final error = result.error!.data as ResultErrorModel;
          final String? message = error.modelState?.erros!
              .map((e) => e)
              .join("\n");

          await OrdersControllerPartLocal.deleteOrder(this);

          SnackbarCustom.snackbarError(message ?? "");
          return;
        }
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> getProductsByOrder(OrderParametersModel parametersModel) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "getProductsByOrder",
    );

    final loading = PlkLoading();
    try {
      loading.show(title: AppStrings.load);

      subAction.reportEvent("parametersModel: $parametersModel");

      final result = await ordersApi.getMixProducts(
        model: parametersModel.paymentTypeParameters!,
      );

      result.when(
        sucess: (data) async {
          globalParams.order.setProducts(data);

          loading.hide();
          // GetC.close();
          await loadProducts();
          await productExists();
          update();
        },
        error: (error) {
          loading.hide();
          SnackbarCustom.snackbarError(error.error);
        },
      );
    } catch (e, s) {
      loading.hide();
      subAction.reportZoneStacktrace(e, s);
      log(e.toString());
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> getProductsEspByOrder(
    OrderParametersModel parametersModel,
  ) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "getProductsEspByOrder",
    );

    final loading = PlkLoading();
    try {
      loading.show(title: AppStrings.load);
      final result = await ordersApi.getTabloidProducts(
        model: parametersModel.paymentTypeParametersSpecial!,
      );
      result.when(
        sucess: (data) async {
          try {
            productsTabloid = data;
            await getTabloidProductsSpecial();

            await loadProductsEsp();

            await productExistsTabloid();
            update();
          } catch (e) {
            SnackbarCustom.snackbarError(e.toString());
          } finally {
            loading.hide();
          }
        },
        error: (error) {
          loading.hide();
          SnackbarCustom.snackbarError(error.error);
        },
      );
    } catch (e, s) {
      loading.hide();
      subAction.reportZoneStacktrace(e, s);
      log(e.toString());
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> productExistsTabloid() async {
    if (isEditOrder == true) {
      if (productListFull.isEmpty &&
          (productsTabloid!.combosOferta != null &&
              productsTabloid!.combosOferta!.isEmpty)) {
        await OrdersControllerPartLocal.deleteOrder(this);
        await Dialogs.info(
          AppStrings.attention,
          "O tabloide não possui produtos ou combos para o pedido especial. O pedido será excluído.",
          buttonName: "Entendi".toUpperCase(),
          buttonOnPressed: () async {
            Get.back();
            Get.until((route) => Get.currentRoute == RoutesPath.reportOrders);
          },
        );
      }
    }
  }

  Future<void> productExists() async {
    if (isEditOrder == true) {
      if (productListFull.isEmpty &&
          (globalParams.getProducts()!.produtos!.combosOferta!.isEmpty)) {
        await OrdersControllerPartLocal.deleteOrder(this);
        await Dialogs.info(
          AppStrings.attention,
          "O pedido não possui produtos ou combos para o pedido. O pedido será excluído.",
          buttonName: "Entendi".toUpperCase(),
          buttonOnPressed: () async {
            Get.back();
            Get.until((route) => Get.currentRoute == RoutesPath.reportOrders);
          },
        );
      }
    }
  }

  List<DistributorsModel> getDistributorList(SyncronizationModel orderEdit) {
    List<DistributorsModel> distributorList = [];

    for (var info in orderEdit.payLoad!.orderInfo!) {
      for (var pay in info.payLoad!) {
        if (pay.distribuidores!.isNotEmpty) {
          pay.distribuidores!
              .map(
                (e) => distributorList.add(
                  DistributorsModel(
                    distributorsId: e.distribuidorId,
                    name: e.distribuidor!.nomeFantasia!,
                    payLoad: pay,
                    distribuidor: DistribuidorModel(
                      id: e.distribuidor!.id,
                      ativo: e.distribuidor!.ativo,
                      habilitaContaCorrente:
                          e.distribuidor!.habilitaContaCorrente,
                      nomeFantasia: e.distribuidor!.nomeFantasia,
                      razaoSocial: e.distribuidor!.razaoSocial,
                      valorMinimoDePedido: e.distribuidor!.valorMinimoDePedido,
                    ),
                    isSelected: e.selected,
                    ordemDePreferencia: e.ordemDePreferencia,
                    ordemMelhorAtendimento: e.ordemMelhorAtendimento,
                    ordemSelected: e.oldOrdemDePreferencia,
                  ),
                ),
              )
              .toList();
        } else if (pay.distribuidor != null) {
          final e = pay.distribuidor!;
          distributorList.add(
            DistributorsModel(
              distributorsId: e.distribuidorId,
              name: e.distribuidor!.nomeFantasia!,
              payLoad: pay,
              distribuidor: DistribuidorModel(
                id: e.distribuidor!.id,
                ativo: e.distribuidor!.ativo,
                habilitaContaCorrente: e.distribuidor!.habilitaContaCorrente,
                nomeFantasia: e.distribuidor!.nomeFantasia,
                razaoSocial: e.distribuidor!.razaoSocial,
                valorMinimoDePedido: e.distribuidor!.valorMinimoDePedido,
              ),
              isSelected: e.selected,
              ordemDePreferencia: e.ordemDePreferencia,
              ordemMelhorAtendimento: e.ordemMelhorAtendimento,
              ordemSelected: e.oldOrdemDePreferencia,
            ),
          );
        }
      }
    }
    return distributorList;
  }

  bool canShowMdtr(MetricaMdtr? metricaMdtr) {
    final params =
        generalParameterizationController
            .generalSettingsOrderDiscountRegistration!;

    if (globalParams.getTypeOrderId() == TyperOrderEnum.padrao) {
      return false;
    }

    if (params.exibeEstrategiasAdicionaisREPEsp == true &&
        metricaMdtr != null) {
      return true;
    } else {
      return false;
    }
  }

  Future<void> loadAlphabet() async {
    List<String?> productsName;

    if (globalParams.getTypeOrderId() == TyperOrderEnum.especial) {
      productsName =
          productsTabloid!.produtos!
              .where((x) => x.isDemonstraGridPedido == true)
              .map((e) => e.descricao)
              .toList();
    } else {
      productsName =
          globalParams.order.products!.produtos!.skus!
              .where((x) => x.isDemonstraGridPedido == true)
              .map((e) => e.descricao)
              .toList();
    }

    for (var item in alphabet) {
      if (productsName.any(
        (element) =>
            element != null && element.toUpperCase().startsWith(item.letter!),
      )) {
        item.hasProduct = true;
      }
    }
  }

  Future<bool> validateDiscountByItemSpecial() async {
    final params =
        generalParameterizationController
            .generalSettingsOrderDiscountRegistration!;
    List<OrdersProductsListModel> invalidItems = [];

    final shoppingCartItens =
        productListFull.where((element) => element.qtdy! > 0).toList();

    if (shoppingCartItens.isEmpty) {
      return true;
    }

    for (var product in shoppingCartItens) {
      try {
        //simular o descont fora do permitido
        // product.discountChange = 250;
        double discountChange = product.discountChange ?? 0.0;
        if (product.discountRangeCurrent != null) {
          if (product.discountRangeCurrent!.discountMin! > discountChange &&
              params.limitarReducaoDescontoMinimoEspecial!) {
            invalidItems.add(product);
          } else if (product.discountRangeCurrent!.discountMax! <
              discountChange) {
            invalidItems.add(product);
          }
        }
      } on FormatException {
        invalidItems.add(product);
      }
    }

    if (invalidItems.isNotEmpty) {
      String errorMessage =
          "O desconto está fora do valor permitido para os seguintes produtos:\n\n";
      for (var product in invalidItems) {
        errorMessage += "- ${product.name}\n\n";
      }

      await Dialogs.info(AppStrings.attention, errorMessage, buttonName: "Ok");
      return false;
    } else {
      return true;
    }
  }

  GeneralSettingsOrderDiscountRegistrationModel getParamOrderDiscount() {
    return generalParameterizationController
        .generalSettingsOrderDiscountRegistration!;
  }

  bool isShowPriceWithRepST(OrdersProductsListModel data) {
    //Se for perfil Loja ou Rede de Farmacia, não mostrar o preço com ST
    if (appController.userLogged?.perfilId! ==
            'ec97c4fb-3879-459d-884f-f585fb824cbd' ||
        appController.userLogged?.perfilId ==
            '386bd79a-fe11-4801-ba85-b497c646713f') {
      return false;
    }

    return (globalParams.getTypeOrderId() == TyperOrderEnum.rep &&
            data.showPriceWithRepST == true) ||
        (globalParams.getTypeOrderId() == TyperOrderEnum.especial &&
            data.showPriceWithSpecialST == true);
  }
}
