import 'package:dio/dio.dart';
import 'package:dynatrace_flutter_plugin_dio/dynatrace_flutter_plugin_dio.dart';
import 'package:pharmalink/app/app_exports.dart';
import 'package:pharmalink/app/controller/parameter_controller.dart';
import 'package:pharmalink/app/controller/theme_controller.dart';
import 'package:pharmalink/config/databases/database_init.dart';
import 'package:pharmalink/config/databases/ilocal_db.dart';
import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/core/utils/dynatrace/dynatrace_custom.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/home/<USER>/home_api.dart';
import 'package:pharmalink/modules/home/<USER>/home_controller.dart';

abstract class DependencyInjection {
  static Future<void> init() async {
    await Get.putAsync<DynatraceCustom>(()=>DynatraceCustom().init(), permanent: true);
    Get.put<HttpManager>(
      HttpManager(
        Dio(
          BaseOptions(
            connectTimeout: const Duration(minutes: 20),
            receiveTimeout: const Duration(minutes: 20),
          ),
        )..instrument(),
      ),
    );
    Get.put<ConnectivityController>(ConnectivityController());
    Get.put<ILocalDb>(LocalDb());
    //Register Controllers
    Get.put<AppController>(AppController());
    Get.put<ParameterController>(ParameterController());
    Get.put<ThemeController>(ThemeController());
    // Get.put<InitializeController>(InitializeController());
    Get.put<BeginController>(BeginController());
    Get.put<WorkspacesController>(WorkspacesController());
    Get.put<LoginController>(LoginController());
    Get.put<SynchronizationsController>(SynchronizationsController());
    Get.put<ThemesController>(ThemesController());
    Get.put<ReportsController>(ReportsController());
    // Get.put<StoresController>(StoresController());

    Get.put<StoreRoutesPanelController>(StoreRoutesPanelController());
    Get.put<StoreRoutesPlannedController>(StoreRoutesPlannedController());
    Get.put<StoreRoutesController>(StoreRoutesController());

    Get.put<StoreParametersController>(StoreParametersController());
    Get.put<NavigationPageController>(NavigationPageController());
    Get.put<SettingsAppController>(SettingsAppController());

    //Register Apis
    Get.put<IWorkspacesApi>(WorkspacesApi(Get.find()));
    Get.put<ILoginApi>(LoginApi(Get.find()));
    Get.put<IThemesApi>(ThemesApi(Get.find()));
    Get.put<IStoresApi>(StoresApi(Get.find()));
    Get.put<IStoreParametersApi>(StoreParametersApi(Get.find()));
    Get.put<RoutesController>(RoutesController());
    Get.put<IRoutesApi>(RoutesApi(Get.find()));

    Get.put<GeneralParameterizationController>(
        GeneralParameterizationController());
    Get.put<IGeneralParameterizationApi>(
        GeneralParameterizationApi(Get.find()));

    Get.put<IReportOrdersApi>(ReportOrdersApi(Get.find()));
    Get.put<IHomeApi>(HomeApi(Get.find()));
    Get.put<HomeController>(HomeController());

    Get.put<LogsHttpController>(LogsHttpController());

    Get.put<ISynchronizationsOfflineApi>(
        SynchronizationsOfflineApi(Get.find()));

    // await Get.putAsync<SynchronizationsOfflineService>(
    //     () async => await SynchronizationsOfflineService().init());

    await Get.putAsync<StoreRoutesSyncService>(
        () async => await StoreRoutesSyncService().init());

    await Get.putAsync<StoreRoutesOfflineSyncService>(
        () async => await StoreRoutesOfflineSyncService().init());
  }
}
