import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/report_orders/models/uf_model.dart';
import 'package:pharmalink/widgets/dropdown/custom_search_dropdown.dart';

class UfDropdownWidget extends StatelessWidget {
  final List<UfModel> ufs;
  final UfModel? selectedUf;
  final void Function(UfModel?) onSelected;
  final TextEditingController controller;
  final VoidCallback? onClear;

  const UfDropdownWidget({
    super.key,
    required this.ufs,
    required this.selectedUf,
    required this.onSelected,
    required this.controller,
    this.onClear,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: CustomSearchDropdown<UfModel>(
            items: ufs,
            selected: selectedUf,
            onSelected: onSelected,
            controller: controller,
            labelBuilder: (uf) => uf.sigla,
            hintText: 'Selecione uma UF',
          ),
        ),
        if (selectedUf != null && onClear != null)
          CustomInkWell(
            onTap: onClear,
            child: Icon(FontAwesomeIcons.xmark, color: Colors.grey),
          ),
      ],
    );
  }
}
