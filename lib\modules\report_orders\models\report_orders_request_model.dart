class ReportOrdersListRequest {
  int? pagina;
  int? tamanhoPagina;
  String? userId;
  String? perfil;
  String? dataInicio;
  String? dataTermino;
  String? idPedido;
  String? cnpjRazaoSocial;
  String? usuario;
  List<String>? tipoProduto;
  String? uf;

  ReportOrdersListRequest({
    this.pagina,
    this.tamanhoPagina,
    this.userId,
    this.perfil,
    this.dataInicio,
    this.dataTermino,
    this.idPedido,
    this.cnpjRazaoSocial,
    this.usuario,
    this.tipoProduto,
    this.uf,
  });

  ReportOrdersListRequest.fromJson(Map<String, dynamic> json) {
    pagina = json['pagina'];
    tamanhoPagina = json['tamanhoPagina'];
    userId = json['UserId'];
    perfil = json['Perfil'];
    dataInicio = json['DataInicio'];
    dataTermino = json['DataTermino'];
    idPedido = json['IdPedido'];
    cnpjRazaoSocial = json['CnpjRazaoSocial'];
    usuario = json['Username'];
    tipoProduto = json['Categoria'];
    uf = json['Uf'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['pagina'] = pagina;
    data['tamanhoPagina'] = tamanhoPagina;
    data['UserId'] = userId;
    data['Perfil'] = perfil;
    data['DataInicio'] = dataInicio;
    data['DataTermino'] = dataTermino;
    data['IdPedido'] = idPedido;
    data['CnpjRazaoSocial'] = cnpjRazaoSocial;
    data['Username'] = usuario;
    data['Categoria'] = tipoProduto;
    data['Uf'] = uf;
    return data;
  }
}
