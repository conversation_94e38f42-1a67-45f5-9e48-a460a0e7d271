name: pharmalink
description: PharmaLink Mobile by Flutter
publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 2.5.0+190

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  device_preview: ^1.1.0
  archive: ^4.0.7
  brasil_fields: ^1.14.0
  cached_network_image: ^3.4.1
  camera: ^0.11.1
  connectivity_plus: ^6.1.4
  crop_image: ^1.0.16
  crypto: ^3.0.6
  cupertino_icons: ^1.0.8
  device_info_plus: ^11.4.0
  diacritic: ^0.1.6
  dio: ^5.8.0+1
  dio_smart_retry: ^7.0.1
  dynatrace_flutter_plugin: ^3.315.1
  dynatrace_flutter_plugin_dio: ^3.315.1
  dots_indicator: ^4.0.1
  encrypt: ^5.0.3
  expansion_tile_card: ^3.0.0
  expansion_tile_group: ^2.2.0
  file_picker: ^8.1.7
  flutter_appauth: ^9.0.0
  flutter_markdown: ^0.7.7
  flutter_masked_text2: ^0.9.1
  flutter_monitoring_health: ^0.0.1+1
  flutter_reorderable_list: ^1.3.1
  flutter_screenutil: ^5.9.3
  flutter_svg: ^2.1.0
  font_awesome_flutter: ^10.8.0
  freezed_annotation: ^2.4.4
  gap: ^3.0.1
  geolocator: ^14.0.0
  get: ^4.7.2
  get_storage: ^2.1.1
  google_fonts: ^6.2.1
  http: ^1.4.0
  image: ^4.5.4
  flutter_image_compress: ^2.4.0
  image_picker: ^1.1.2
  intl: ^0.19.0
  jwt_decode: ^0.3.1
  lottie: ^3.3.1
  maps_launcher: ^3.0.0
  open_file: ^3.5.10
  package_info_plus: ^8.3.0
  path: ^1.9.1
  path_provider: ^2.1.5
  pdf: ^3.11.3
  permission_handler: ^12.0.0
  photo_view: ^0.15.0
  share_plus: ^11.0.0
  shared_preferences: ^2.5.3
  sqflite: ^2.4.2
  sqflite_common_ffi: ^2.3.5
  tuple: ^2.0.2
  url_launcher: ^6.3.1
  uuid: ^4.5.1
  webview_flutter: ^4.11.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_native_splash: ^2.4.6
  build_runner: ^2.4.15
  flutter_lints: ^5.0.0
  flutter_flavorizr: ^2.4.1
  flutter_launcher_icons: ^0.14.4

flutter_native_splash:
  color: "#FFFFFF"
  image: assets/app/splash-default.png
  android: true
  ios: true
  web: false

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/sync/
    - assets/images/pedidos/
    - assets/images/research/
    - assets/app/
    - assets/app/itrade/
    - assets/app/pharmalink/
    - assets/animations/
    - assets/fonts/
    - assets/data/

fonts:
  - family: Gilroy
    fonts:
      - asset: assets/fonts/gilroy/Gilroy-Bold.ttf
        weight: 800
      - asset: assets/fonts/gilroy/Gilroy-Medium.ttf
        weight: 700
      - asset: assets/fonts/gilroy/Gilroy-Regular.ttf
        weight: 400
  - family: Nexa
    fonts:
      - asset: assets/fonts/nexa/Nexa-Bold.otf
        weight: 800
      - asset: assets/fonts/nexa/Nexa-Regular.otf
        weight: 400
      - asset: assets/fonts/nexa/Nexa-Light.otf
        weight: 300

flavorizr:
  ide: "idea"
  app:
    android:
      flavorDimensions: "flavor-type"
    

  flavors:
    itrade:
      app:
        name: "iTrade"
      android:
        applicationId: "br.com.interplayers.itrade"
      ios:
        bundleId: "br.com.interplayers.itrade"
      native_splash:
        color: "#FFFFFF" # Vermelho para Apple
        image: "assets/app/itrade/splash.png"
    pharmalink:
      app:
        name: "Pharmalink"
      android:
        applicationId: "br.com.interplayers.pharmalink"
      ios:
        bundleId: "br.com.interplayers.pharmalink"
      native_splash:
        color: "#FFFFFF" # Vermelho para Apple
        image: "assets/app/pharmalink/splash.png"
