import 'package:dynatrace_flutter_plugin/dynatrace_flutter_plugin.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:pharmalink/app/controller/app_controller.dart';
import 'package:pharmalink/app_constants.dart';

import 'dynatrace_custom_action.dart';

class DynatraceCustom {
  Future<DynatraceCustom> init() async {
    await Dynatrace().startWithoutWidget();
    Dynatrace().applyUserPrivacyOptions(
      UserPrivacyOptions(DataCollectionLevel.UserBehavior, true),
    );


    return this;
  }

  void informaAmbiente({
    DynatraceAction? action,
    required Environment environment,
  }) {
    var key = "Ambiente";

    if (action == null) {
      Dynatrace().reportStringValue(key, environment.name.capitalize);
    } else {
      action.reportStringValue(key, environment.name.capitalize);
    }
  }

  Future<void> collectAndReportSessionData() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();

    var username = appController.userLogged?.userName?.toLowerCase().trim();
    if (username != null) {
      Dynatrace().identifyUser(username);
    }

    DynatraceRootAction reportAll = Dynatrace().enterAction("Session Data");
    try {
      informaAmbiente(
        environment: appController.currentEnvironment,
      );

      informaAmbiente(
        action: reportAll,
        environment: appController.currentEnvironment,
      );

      reportAll.reportStringValue(
        "Package Name",
        packageInfo.packageName.toString(),
      );

      reportAll.reportStringValue("Version", packageInfo.version.toString());

      if (username != null) {
        reportAll.reportStringValue(
          "Workspace",
          appController.workspace?.name ?? "Workspace not selected",
        );

        reportAll.reportStringValue("User", username);
      }
    } finally {
      reportAll.leaveAction();
      Dynatrace().flushEvents();
    }
  }

  Future<void> reportZoneStacktrace({
    required dynamic error,
    required StackTrace stacktrace,
  }) async => await Dynatrace().reportZoneStacktrace(error, stacktrace);

  DynatraceCustomAction actionReport({
    required Type runtimeType,
    required String tagIdentifier,
    String? valueName,
    String? value,
  }) {
    var actionName = "[${runtimeType.toString()}] $tagIdentifier";

    var storeData = globalParams.getCurrentStore();
    if (storeData != null) {
      actionName = "$actionName: ${storeData.cNPJ}";
    }
    var action = Dynatrace().enterAction(actionName);
    if (storeData?.cNPJ?.trim().isNotEmpty == true) {
      action.reportStringValue('PDV', storeData!.cNPJ!.trim());
    }
    if (valueName != null) {
      action.reportStringValue(valueName, value);
    }
    var customAction = DynatraceCustomAction(action, actionName);

    return customAction;
  }

  void reportErrorStacktrace({
    required String errorName,
    required String errorValue,
    required String reason,
    required StackTrace stacktrace,
  }) => Dynatrace().reportErrorStacktrace(
    errorName,
    errorValue,
    reason,
    stacktrace.toString(),
  );
}
