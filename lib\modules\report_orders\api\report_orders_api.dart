import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/core/models/result_error_model.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/orders/models/product_type_model.dart';
import 'package:pharmalink/modules/report_orders/models/mirror_p_d_f_response.dart';
import 'package:pharmalink/modules/report_orders/models/report_orders_attachment_request.dart';
import 'package:pharmalink/modules/report_orders/models/report_orders_attachment_response.dart';
import 'package:pharmalink/modules/report_orders/models/report_orders_mirror_response_model.dart';
import 'package:pharmalink/modules/report_orders/models/report_orders_request_model.dart';
import 'package:pharmalink/modules/report_orders/models/report_orders_response_model.dart';
import 'package:pharmalink/modules/report_orders/models/uf_model.dart';

abstract class IReportOrdersApi {
  Future<HttpResponse<ReportOrdersListResponse>> getReportOrdersAnalitycs({
    required ReportOrdersListRequest model,
  });

  Future<HttpResponse<MirrorOrderResponse>> getMirrorOrder({
    required String orderNumber,
  });

  Future<HttpResponse<List<TiposProdutosModel>>> getProductTypes();
  Future<HttpResponse<ReportOrdersAttachmentResponse>> downloadAttachment({
    required ReportOrdersAttachmentRequest model,
  });
  Future<HttpResponse<MirrorPDFResponse>> getMirrorOrderPdf({
    required String orderNumber,
  });

  Future<HttpResponse<List<UfModel>>> getUfs();
}

class ReportOrdersApi extends IReportOrdersApi {
  final HttpManager _httpManager;
  ReportOrdersApi(this._httpManager);

  @override
  Future<HttpResponse<ReportOrdersListResponse>> getReportOrdersAnalitycs({
    required ReportOrdersListRequest model,
  }) async {
    return await _httpManager.requestFull<
      ReportOrdersListResponse,
      ResultErrorModel
    >(
      path:
          'relatorioAnaliticoPedido/listarRelatorioAnaliticoAgrupadoPorPedido',
      method: HttpMethods.post,
      body: model.toJson(),
      parser: (data) {
        return ReportOrdersListResponse.fromJson(data);
      },
      parserError: (dynamic errorData) {
        return ResultErrorModel.fromJson(errorData);
      },
    );
  }

  @override
  Future<HttpResponse<MirrorOrderResponse>> getMirrorOrder({
    required String orderNumber,
  }) async {
    return await _httpManager.request<MirrorOrderResponse>(
      path: 'pedidos/obterEspelhoPedidoPorId/$orderNumber',
      method: HttpMethods.get,
      parser: (data) {
        return MirrorOrderResponse.fromJson(data);
      },
    );
  }

  @override
  Future<HttpResponse<ReportOrdersAttachmentResponse>> downloadAttachment({
    required ReportOrdersAttachmentRequest model,
  }) async {
    return await _httpManager
        .requestFull<ReportOrdersAttachmentResponse, ResultErrorModel>(
          path: 'anexoPedido/buscar',
          method: HttpMethods.post,
          body: model.toJson(),
          parser: (data) {
            return ReportOrdersAttachmentResponse.fromJson(data);
          },
          parserError: (dynamic errorData) {
            return ResultErrorModel.fromJson(errorData);
          },
        );
  }

  @override
  Future<HttpResponse<MirrorPDFResponse>> getMirrorOrderPdf({
    required String orderNumber,
  }) async {
    return await _httpManager.request<MirrorPDFResponse>(
      path: 'pedidos/obterPdfEspelhoPedidoPorId/$orderNumber',
      method: HttpMethods.get,
      parser: (data) {
        return MirrorPDFResponse.fromJson(data);
      },
    );
  }

  @override
  Future<HttpResponse<List<TiposProdutosModel>>> getProductTypes() async {
    return await _httpManager.request<List<TiposProdutosModel>>(
      path: 'tiposProdutos',
      method: HttpMethods.get,
      parser: (data) {
        return (data as List)
            .map((item) => TiposProdutosModel.fromJson(item))
            .toList();
      },
    );
  }

  @override
  Future<HttpResponse<List<UfModel>>> getUfs() async {
    return await _httpManager.request<List<UfModel>>(
      path: 'ufs',
      method: HttpMethods.get,
      parser: (data) {
        return (data as List).map((item) => UfModel.fromJson(item)).toList();
      },
    );
  }
}
