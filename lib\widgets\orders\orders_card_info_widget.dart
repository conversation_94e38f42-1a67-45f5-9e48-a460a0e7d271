//OrdersCardInfoWidget
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class OrdersCardInfoWidget extends StatelessWidget {
  final String? orderType;
  final String? paymentType;
  final List<String>? distributors;
  final String? tabloidName;
  final bool showMixIdealButton;

  const OrdersCardInfoWidget({
    super.key,
    this.orderType,
    this.paymentType,
    this.distributors,
    this.tabloidName,
    this.showMixIdealButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 5, right: 5, bottom: 10),
      child: ExpansionTile(
        title: LabelWidget(
          title:
              orderType != null
                  ? (tabloidName != null
                      ? '$orderType - $tabloidName'
                      : orderType!)
                  : 'Pedido',
          fontSize: DeviceSize.fontSize(18, 24),
          fontWeight: FontWeight.bold,
        ),
        children: [
          if (paymentType != null)
            _buildInfoRow('Tipo de Pagamento:', paymentType!),
          if (distributors != null && distributors!.isNotEmpty)
            _buildDistributorsSection(),
          _buildInfoRow('Usuário:', appController.userLogged?.nome ?? ""),
          if (showMixIdealButton && !ordersController.isEditOrder)
            _buildMixIdealButton(),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 5, left: 20, right: 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LabelWidget(
            title: label,
            fontSize: DeviceSize.fontSize(14, 16),
            fontWeight: FontWeight.bold,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: LabelWidget(
              title: value,
              fontSize: DeviceSize.fontSize(14, 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDistributorsSection() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 5, left: 20, right: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LabelWidget(
            title: 'Distribuidores:',
            fontSize: DeviceSize.fontSize(14, 16),
            fontWeight: FontWeight.bold,
          ),
          const SizedBox(height: 4),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children:
                distributors!.map((distributor) {
                  return Padding(
                    padding: const EdgeInsets.only(left: 16, top: 2),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('• ', style: TextStyle(fontSize: 14)),
                        Expanded(
                          child: LabelWidget(
                            title: distributor,
                            fontSize: DeviceSize.fontSize(14, 16),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildMixIdealButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 5, left: 20, right: 20, top: 10),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: () async {
            await _consultMixIdeal();
          },
          icon: const Icon(
            Icons.analytics_outlined,
            size: 18,
            color: Colors.white,
          ),
          label: LabelWidget(
            title: 'Consultar Mix Ideal',
            fontSize: DeviceSize.fontSize(14, 16),
            fontWeight: FontWeight.w600,
            textColor: Colors.white,
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF2E7D32),
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _consultMixIdeal() async {
    try {
      // Configurar os IDs dos distribuidores
      final distributorIds =
          globalParams.order.currentDistributors
              ?.map((e) => e.distribuidor?.id)
              .where((id) => id != null)
              .cast<int>()
              .toList() ??
          [];

      if (distributorIds.isEmpty) {
        SnackbarCustom.snackbarWarning("Nenhum distribuidor selecionado");
        return;
      }

      ordersIdealMixController.setDistributorId(ids: distributorIds);

      // Verificar se mix ideal está disponível
      final isAvailable = await ordersIdealMixController.mixIdealIsAvailable();

      if (isAvailable) {
        // Navegar para a tela de mix ideal
        await ordersIdealMixController.getData();
      } else {
        SnackbarCustom.snackbarWarning(
          "Mix Ideal não disponível para os distribuidores selecionados",
        );
      }
    } catch (e) {
      SnackbarCustom.snackbarError(
        "Erro ao consultar Mix Ideal: ${e.toString()}",
      );
    }
  }
}
