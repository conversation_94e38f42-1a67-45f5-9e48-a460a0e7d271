import 'dart:developer';

import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders/models/product_type_model.dart';
import 'package:pharmalink/modules/orders_resume/enuns/status_syncronization_enum.dart';
import 'package:pharmalink/modules/report_orders/pages/widgets/order_info_row.dart';
import 'package:pharmalink/modules/report_orders/pages/widgets/report_orders_card_order_widget.dart';
import 'package:pharmalink/modules/report_orders/pages/widgets/report_orders_search_item.dart';
import 'package:pharmalink/modules/report_orders/pages/widgets/uf_dropdown_widget.dart';
import 'package:pharmalink/widgets/dropdown/multi_select_category_field.dart';
import 'package:pharmalink/widgets/headers/header_widget.dart';
import 'package:pharmalink/widgets/label/label_progress.dart';

class ReportOrdersPage extends StatefulWidget {
  const ReportOrdersPage({super.key});

  @override
  State<ReportOrdersPage> createState() => _ReportOrdersPageState();
}

class _ReportOrdersPageState extends State<ReportOrdersPage> {
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    final reportOrdersController = Get.find<ReportOrdersController>();
    _scrollController.addListener(() async {
      log(
        "ScrollController: ${_scrollController.position.pixels} - ${_scrollController.position.maxScrollExtent}",
      );
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (reportOrdersController.storesFull.isNotEmpty) {
          await reportOrdersController.loadMoreData();
        }
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<ReportOrdersController>(
      "ListagemPedidos - ReportOrdersPage",
      builder: (ctrl) {
        return Scaffold(
          backgroundColor: whiteColor,
          appBar: AppBar(
            backgroundColor: themesController.getPrimaryColor(),
            title: LabelWidget(
              title: "Pedidos",
              fontSize: DeviceSize.fontSize(18, 21),
              fontWeight: FontWeight.w600,
              textColor: whiteColor,
            ),
            actions: [
              CustomInkWell(
                onTap:
                    ctrl.isProgress == false
                        ? () async {
                          //await ctrl.syncOrders();

                          Get.toNamed(RoutesPath.reportOrdersSync)?.then((
                            value,
                          ) async {
                            await ctrl.updateListOrdersToSync();
                          });
                        }
                        : null,
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: DeviceSize.height(18, 18),
                  ),
                  child: Icon(
                    FontAwesomeIcons.arrowRotateRight,
                    size: DeviceSize.fontSize(20, 24),
                  ),
                ),
              ),
            ],
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: whiteColor),
              onPressed: () {
                Get.back();
              },
            ),
          ),
          body: RefreshIndicator.adaptive(
            onRefresh: () async {
              Future.delayed(const Duration(seconds: 2), () async {
                await ctrl.updateListOrdersToSync();
              });
            },
            child: ListView(
              controller: _scrollController,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LabelWithProgressWidget(
                        title: "Pedidos a sincronizar",
                        fontSize: DeviceSize.fontSize(13, 16),
                        isProgress: ctrl.isProgress,
                      ),
                      10.toHeightSpace(),
                      SizedBox(
                        height: DeviceSize.height(190, 220),
                        child: ListView.builder(
                          itemCount: ctrl.ordersList.length,
                          scrollDirection: Axis.horizontal,
                          itemBuilder: (context, index) {
                            var order = ctrl.ordersList[index];
                            return ReportOrdersCardOrdersWidget(
                              data: order,
                              orderId: order.transactionKey!,
                              title:
                                  order.storeData?.razaoSocial ??
                                  order.payLoad?.pdvName ??
                                  "-",
                              canEdit: getStatusSynchronizationCanEdit(
                                order.status!,
                              ),
                              state: getStatusSynchronizationText(
                                order.status ?? 0,
                              ),
                              stateColor: getStatusSynchronizationColor(
                                order.status ?? 0,
                              ),
                              date: order.createAt!.formatDate(
                                formatType: DateFormatType.ddMMyyyy,
                              ),
                              isOnline: order.isOnline ?? true,
                              fileAttachmentStatus:
                                  order.fileAttachment?.status,
                              canIncludeFile: getStatusSynchronizationCanEdit(
                                order.status!,
                              ), //&& (order.fileAttachment == null || order.fileAttachment!.status == FileAttachmentStatus.waiting),
                            );
                          },
                        ),
                      ),
                      20.toHeightSpace(),
                      const HeaderWidget(title: "Filtro de Pedidos"),
                      10.toHeightSpace(),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            LabelWidget(
                              title: "Período:",
                              fontSize: DeviceSize.fontSize(14, 16),
                            ),
                            const Spacer(),
                            CustomInkWell(
                              onTap: () async {
                                DateTime? picked = await showDatePicker(
                                  context: context,
                                  initialDate: ctrl.startDate ?? DateTime.now(),
                                  firstDate: DateTime(1980),
                                  lastDate: DateTime(2101),
                                  helpText: "Selecione a Data Inicial",
                                  fieldLabelText: "Data Inicial",
                                  fieldHintText: "Digite uma data",
                                );
                                if (picked != null) {
                                  ctrl.setStartDate(picked);
                                }
                              },
                              child: LabelWidget(
                                title: ctrl.startDateStr,
                                fontSize: DeviceSize.fontSize(14, 20),
                              ),
                            ),
                            10.toWidthSpace(),
                            CustomInkWell(
                              onTap: () async {
                                DateTime? picked = await showDatePicker(
                                  context: context,
                                  initialDate: ctrl.endDate ?? DateTime.now(),
                                  firstDate: DateTime(1980),
                                  lastDate: DateTime(2101),
                                  helpText: "Selecione a Data Final",
                                  fieldLabelText: "Data Final",
                                  fieldHintText: "Digite uma data",
                                );
                                if (picked != null) {
                                  ctrl.setEndDate(picked);
                                }
                              },
                              child: LabelWidget(
                                title: ctrl.endDateStr,
                                fontSize: DeviceSize.fontSize(14, 20),
                              ),
                            ),
                            10.toWidthSpace(),
                            IconButton(
                              onPressed: () async {
                                DateTime? startPicked = await showDatePicker(
                                  context: Get.context!,
                                  initialDate: ctrl.startDate ?? DateTime.now(),
                                  firstDate: DateTime(1980),
                                  lastDate: DateTime(2101),
                                  helpText: "Selecione a Data Inicial",
                                  fieldLabelText: "Data Inicial",
                                  fieldHintText: "Digite uma data",
                                );

                                if (startPicked != null) {
                                  ctrl.setStartDate(startPicked);
                                  DateTime? endPicked = await showDatePicker(
                                    context: Get.context!,
                                    initialDate: ctrl.endDate ?? startPicked,
                                    firstDate: startPicked,
                                    lastDate: DateTime(2101),
                                    helpText: "Selecione a Data Final",
                                    fieldLabelText: "Data Final",
                                    fieldHintText: "Digite uma data",
                                  );
                                  if (endPicked != null) {
                                    ctrl.setEndDate(endPicked);
                                  }
                                }
                              },
                              icon: Icon(
                                FontAwesomeIcons.calendarDay,
                                color: Colors.black,
                                size: DeviceSize.fontSize(16, 20),
                              ),
                            ),
                            IconButton(
                              onPressed: () {
                                ctrl.setEndDate(null);
                                ctrl.setStartDate(null);
                              },
                              icon: Icon(
                                FontAwesomeIcons.trash,
                                color: Colors.black,
                                size: DeviceSize.fontSize(16, 20),
                              ),
                            ),
                          ],
                        ),
                      ),
                      OrderInfoRow(
                        title: "PDV:",
                        controller: ctrl.pdvController,
                        onChanged: ctrl.setPdvText,
                        trailingIcon:
                            (ctrl.pdvText != null && ctrl.pdvText!.isNotEmpty)
                                ? FontAwesomeIcons.xmark
                                : null,
                        trailingTap: () {
                          ctrl.clearPdvText();
                        },
                        hintText: "Digite o CNPJ / Razão Social",
                      ),
                      Visibility(
                        visible: ctrl.hasPdvList,
                        child: Container(
                          padding: const EdgeInsets.all(16.0),
                          height: 150,
                          width: double.infinity,
                          child: Card(
                            elevation: 5,
                            child: ListView.builder(
                              itemCount: ctrl.storesFull.length,
                              itemBuilder: (context, index) {
                                final store = ctrl.storesFull[index];
                                return CustomInkWell(
                                  onTap: () {
                                    ctrl.setPdvChoice(store);
                                  },
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: LabelWidget(
                                      fontSize: DeviceSize.fontSize(14, 16),
                                      title:
                                          "${store.cNPJ} - ${(store.razaoSocial ?? "-")}",
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                      Visibility(
                        visible: ctrl.pdvChoice != null,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Flexible(
                                child: LabelWidget(
                                  fontSize: DeviceSize.fontSize(10, 12),
                                  title:
                                      "${(ctrl.pdvChoice?.razaoSocial ?? "-")} - ${ctrl.pdvChoice?.cNPJ ?? "-"}",
                                ),
                              ),
                              IconButton(
                                onPressed: () {
                                  ctrl.setPdvChoice(null);
                                },
                                icon: const Icon(FontAwesomeIcons.xmark),
                              ),
                            ],
                          ),
                        ),
                      ),
                      OrderInfoRow(
                        title: "Nº Pedido Original:",
                        controller: ctrl.numberOrderController,
                        keyboardType: TextInputType.number,
                        onChanged: ctrl.setNumberOrder,
                        trailingIcon:
                            (ctrl.numberOrder != null &&
                                    ctrl.numberOrder!.isNotEmpty)
                                ? FontAwesomeIcons.xmark
                                : null,
                        trailingTap: () {
                          ctrl.clearNumberOrder();
                        },
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                LabelWidget(
                                  title: "Categoria:",
                                  fontSize: 14.sp,
                                ),
                                const Gap(10),
                                Tooltip(
                                  message:
                                      'Ao utilizar este filtro, serão exibidos todos os pedidos que contenham pelo menos um item da categoria escolhida',
                                  child: Icon(
                                    Icons.info,
                                    size: 24,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                            30.toWidthSpace(),
                            Expanded(
                              child: MultiSelectCategoryField<
                                TiposProdutosModel
                              >(
                                allItems: ctrl.productTypes,
                                selectedItems: ctrl.selectedProductTypes,
                                itemLabel: (item) => item.descricao ?? 'N/A',
                                areItemsEqual:
                                    (a, b) =>
                                        a.idTipoProduto == b.idTipoProduto,
                                onCategoryToggled:
                                    ctrl.toggleProductTypeSelection,
                                onClearAll: ctrl.clearSelectedProductTypes,
                                displayController: ctrl.productTypeController,
                                hintText: 'Selecionar categorias',
                              ),
                            ),
                          ],
                        ),
                      ),
                      OrderInfoRow(
                        title: "Usuário:",
                        controller: ctrl.userController,
                        onChanged: ctrl.setSelectedUser,
                        trailingIcon:
                            (ctrl.selectedUser != null &&
                                    ctrl.selectedUser!.isNotEmpty)
                                ? FontAwesomeIcons.xmark
                                : null,
                        trailingTap: ctrl.clearSelectedUser,
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            LabelWidget(title: "UF:", fontSize: 14.sp),
                            30.toWidthSpace(),
                            Expanded(
                              child: UfDropdownWidget(
                                ufs: ctrl.ufs,
                                selectedUf: ctrl.selectedUf,
                                onSelected: ctrl.setSelectedUf,
                                controller: ctrl.ufController,
                                onClear: ctrl.clearSelectedUf,
                              ),
                            ),
                          ],
                        ),
                      ),
                      10.toHeightSpace(),
                      Align(
                        alignment: Alignment.centerRight,
                        child: PrimaryButtonWidget(
                          titleButtom: "Buscar".toUpperCase(),
                          width: 120,
                          borderRadius: 0,
                          onTap: () async {
                            await ctrl.searchOrders(isClear: true);
                          },
                        ),
                      ),
                      10.toHeightSpace(),
                      const HeaderWidget(title: "Últimos pedidos"),
                      ctrl.ordersSearchList.isEmpty
                          ? !ctrl.isLoadMore
                              ? SizedBox(
                                width: MediaQuery.of(context).size.width,
                                height: 100,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    LabelWidget(
                                      title: "Nenhum pedido encontrado",
                                      fontSize: DeviceSize.fontSize(14, 20),
                                    ),
                                  ],
                                ),
                              )
                              : const SizedBox.shrink()
                          : Column(
                            children:
                                ctrl.ordersSearchList.map((order) {
                                  return ReportOrdersSearchItemWidget(
                                    item: order,
                                  );
                                }).toList(),
                          ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          bottomNavigationBar: Visibility(
            visible: ctrl.isLoadMore,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator.adaptive(),
                  ),
                  10.toWidthSpace(),
                  const LabelWidget(title: "Carregando Pedidos"),
                ],
              ),
            ),
          ),
          bottomSheet: const VersionWidget(),
        );
      },
    );
  }
}
