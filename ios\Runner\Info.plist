<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>$(BUNDLE_DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(BUNDLE_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>DTXApplicationID</key>
	<string>40e18b35-0587-42f7-b3ba-4a1dfcfc7d5e</string>
	<key>DTXAutoStart</key>
	<true/>
	<key>DTXBeaconURL</key>
	<string>https://bf53667gkn.bf.dynatrace.com/mbeacon</string>
	<key>DTXCrashReportingEnabled</key>
	<true/>
	<key>DTXFlavor</key>
	<string>flutter</string>
	<key>DTXLogLevel</key>
	<string>ALL</string>
	<key>DTXStartupLoadBalancing</key>
	<true/>
	<key>DTXUserOptIn</key>
	<true/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>sms</string>
		<string>tel</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>Este aplicativo precisa acessar sua câmera para tirar fotos das visitas e responder a pesquisas. As fotos serão usadas para documentar as visitas e garantir a precisão das respostas. Seus dados não serão compartilhados com terceiros.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Este app precisa acessar sua localização mesmo em segundo plano para registrar com precisão o check-in e check-out durante as visitas. Isso nos permite confirmar sua presença no local. Seus dados de localização são usados somente para essa finalidade e nunca serão compartilhados com terceiros.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Este aplicativo precisa acessar sua localização mesmo quando o app estiver em segundo plano para realizar check-in e check-out durante as visitas. Isso confirma que o representante estava no local. Sua localização será usada apenas para este propósito e não será compartilhada com terceiros.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Precisamos acessar sua localização para realizar o check-in e check-out durante as visitas, confirmando que o representante estava no local. Seus dados de localização serão usados apenas para garantir a precisão das visitas e não serão compartilhados com terceiros.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Este app precisa de acesso ao microfone para gravar áudio para na gravação de videos.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Precisa de acesso à biblioteca de fotos para salvar fotos.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Precisa de acesso à biblioteca de fotos para obter fotos.</string>
	<key>NSPrivacyAccessedAPITypes</key>
	<array>
		<dict>
			<key>NSPrivacyAccessedAPICategoryFileTimestamp</key>
			<string>Este aplicativo acessa timestamps de arquivos para organizar documentos.</string>
		</dict>
		<dict>
			<key>NSPrivacyAccessedAPICategoryDiskSpace</key>
			<string>O aplicativo verifica o espaço em disco para garantir armazenamento adequado de dados.</string>
		</dict>
		<dict>
			<key>NSPrivacyAccessedAPICategoryUserDefaults</key>
			<string>UserDefaults são usados para manter suas preferências de configurações.</string>
		</dict>
	</array>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
