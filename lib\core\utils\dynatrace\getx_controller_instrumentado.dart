import 'package:get/get.dart';
import 'package:pharmalink/app_constants.dart';

import 'dynatrace_custom_action.dart';

abstract class GetxControllerInstrumentado<
    T extends GetxControllerInstrumentado<T>> extends GetxController {
  DynatraceCustomAction? _action, _anotherAction;
  String? _tagName;

  void initDynatraceAction(String? tagName) {
    _tagName ??= tagName ?? runtimeType.toString();
    _action ??= dynatrace.actionReport(runtimeType:runtimeType, tagIdentifier:  _tagName!);
  }

  DynatraceCustomAction get dynatraceAction => _getDynatraceAction();

  DynatraceCustomAction _getDynatraceAction() {
    if (_anotherAction != null) {
      return _anotherAction!;
    }

    if (_action == null) {
      initDynatraceAction(runtimeType.toString());
    }
    return _action!;
  }

  T withDynatraceAction(DynatraceCustomAction anotherAction) {
    _anotherAction = anotherAction;
    return this as T;
  }

  T withControllerAction(GetxControllerInstrumentado anotherController) {
    _anotherAction = anotherController.dynatraceAction;
    return this as T;
  }

  void disposeDynatraceActions() {
    _action?.leaveRemainingActions();
    _anotherAction = null;
  }

  void reportEvent(String event) {
    dynatraceAction.reportEvent(event);
  }
}
