import 'package:pharmalink/core/enuns/product_state_enum.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders/models/orders_footer_model.dart';
import 'package:pharmalink/modules/orders/models/orders_local_data_model.dart';
import 'package:pharmalink/modules/orders/models/orders_products_model.dart';
import 'package:pharmalink/modules/orders/models/orders_tabloid_products_response_model.dart';
import 'package:pharmalink/modules/stores_parameters/enuns/type_order_enum.dart';

class OrdersComboController
    extends GetxControllerInstrumentado<OrdersComboController>
    with TraceableController {
  OrdersComboController();

  TextEditingController searchController = TextEditingController();
  String? searchFilter;
  List<CombosOfertaModel> combosOferta = [];
  List<CombosOfertaModel> combosOfertaFull = [];
  List<CombosOfertaModel> combosOfertaEdit = [];

  OrdersFooterModel footer = OrdersFooterModel(
    qtyReal: 0,
    totalApresentation: 0,
    totalNet: 0,
    totalUnits: 0,
    totalGross: 0,
    discount: 0,
  );

  @override
  Future<void> onReady() async {
    ordersResumeController = Get.find<OrdersResumeController>();
    ordersController = Get.find<OrdersController>();
    combosOferta = [];

    super.onReady();

    Future.delayed(Duration.zero, () async {
      await updateCombosOferta();
      if (combosOferta.isEmpty) {
        await advance();
      } else {
        updateFooter();
      }
    });
  }

  Future<void> updateCombosOferta() async {
    combosOferta.clear();
    var sourceCombos =
        globalParams.getTypeOrderId() == TyperOrderEnum.especial
            ? convertTabloidProductsToCombosOfertaModel(
              ordersController.productsTabloid!.combosOferta!,
            )
            : globalParams.getProducts()?.produtos?.combosOferta;

    if (sourceCombos != null) {
      for (var item in sourceCombos) {
        if (item.focusNode == null) {
          item.focusNode = FocusNode();
          item.qtdyController = TextEditingController(
            text: item.qtdy.toString(),
          );
        }

        item.focusNode!.addListener(() async {
          if (item.focusNode!.hasFocus) {
            item.qtdyController!.selection = TextSelection(
              baseOffset: 0,
              extentOffset: item.qtdyController!.text.length,
            );
          } else {
            if (item.qtdyController!.text.isNotEmpty) {
              await setQtdyEdit(item, int.parse(item.qtdyController!.text));
            }
          }
        });
      }

      sourceCombos.map((e) {
        e.produtos!.sort((a, b) => a.descricao!.compareTo(b.descricao!));
      }).toList();

      if (sourceCombos.isNotEmpty) {
        sourceCombos.map((e) {
          e.qtdyReal = calcularQtdyReal(e.produtos!, e);
        }).toList();
      }

      combosOferta = sourceCombos;
      combosOfertaFull = sourceCombos;
      editComboLoad();
      updateComboStatesAndDistributors();
    }
    await loadOrderLocal();
    updateFooter();
  }

  int getPaymentTypeId() {
    if (ordersController.isEditOrder) {
      if (ordersController.syncOrderEdit!.parameters!.typeOrderId ==
          TyperOrderEnum.especial) {
        return 0;
      } else {
        return ordersController
            .syncOrderEdit!
            .parameters!
            .paymentTypeParameters!
            .idPrazoPagamento!;
      }
    } else {
      return globalParams.getDeadlinePayment()!.idPrazoPagamento!;
    }
  }

  Future<void> loadOrderLocal() async {
    if (!ordersController.isEditOrder) {
      if (globalParams.order.orderLocal != null) {
        //
      }
    }
  }

  void editComboLoad() {
    if (ordersController.isEditOrder == true) {
      // Aplicar dados dos combos editados aos combos carregados
      for (var item in combosOferta) {
        final productEdit = ordersController.editItensComboOrder
            .firstWhereOrNull(
              (e) => e.offerComboId == item.idComboOferta && e.qtdy! > 0,
            );

        if (productEdit != null) {
          item.qtdyController!.text = productEdit.qtdy.toString();
          item.qtdy = productEdit.qtdy;

          item.priceOrder = item.precoCombo! * item.qtdy!;
          item.totalOrder = item.precoComboLiquido! * item.qtdy!;
          item.qtdyReal = calcularQtdyReal(item.produtos!, item);
        }
      }
    }
  }

  void updateComboStatesAndDistributors() {
    for (var combo in combosOferta) {
      if (combo.produtos != null) {
        for (var produto in combo.produtos!) {
          updateProductStatesAndDistributor(produto);
        }

        combo.states = determineComboState(combo.produtos!);
        if (combo.produtos!.isNotEmpty) {
          combo.distributorId = determineComboDistributorId(combo);

          if (ordersController.isEditOrder) {
            if (combo.produtos!.any(
              (produto) => produto.paymentTermId == getPaymentTypeId(),
            )) {
              combo.paymentTermId = getPaymentTypeId();
            } else {
              combo.paymentTermId = combo.produtos![0].paymentTermId;
            }
          } else {
            if (combo.produtos!.any(
              (produto) => produto.paymentTermId == getPaymentTypeId(),
            )) {
              combo.paymentTermId = getPaymentTypeId();
            } else {
              combo.paymentTermId = combo.produtos![0].paymentTermId;
            }
          }
        }
      }
    }
  }

  void updateProductStatesAndDistributor(ProdutosOfertasModel produto) {
    if (globalParams.getTypeOrderId() == TyperOrderEnum.especial) {
      produto.states = ordersController.getProductEspecialStateEnum(
        produto.idProduto,
      );
      produto.distributorId =
          globalParams.getCurrentDistributor()!.distribuidorId;

      produto.paymentTermId = ordersController.getPaymentTermEsp(
        produto.idProduto!,
      );
    } else {
      if (produto.idProdutoDUN == null || produto.idProdutoDUN == 0) {
        produto.states = ordersController.getProductStateEnum(
          produto.idProduto,
        );
        produto.distributorId = ordersController.getProductDistributorId(
          produto.idProduto,
        );
      } else {
        produto.states = ordersController.getProductStateEnumDun(
          produto.idProdutoDUN!,
        );
        produto.distributorId = ordersController.getProductDistributorDunId(
          produto.idProdutoDUN!,
        );
      }

      produto.paymentTermId = ordersController.getPaymentTerm(
        globalParams.getProducts()!.produtosDistribuidores!,
        produto.idProduto!,
      );
    }
  }

  ProductStateEnum determineComboState(List<ProdutosOfertasModel> produtos) {
    if (produtos.isEmpty) {
      return ProductStateEnum.block;
    }
    if (produtos.any((element) => element.states == ProductStateEnum.alert)) {
      return ProductStateEnum.alert;
    }
    if (produtos.any((element) => element.states == ProductStateEnum.block)) {
      return ProductStateEnum.block;
    }
    return ProductStateEnum.done;
  }

  int? determineComboDistributorId(CombosOfertaModel combo) {
    if (combo.states == ProductStateEnum.done ||
        combo.states == ProductStateEnum.alert) {
      return combo.produtos!
          .firstWhere((element) => element.states == combo.states)
          .distributorId;
    }
    return null;
  }

  List<CombosOfertaModel> convertTabloidProductsToCombosOfertaModel(
    List<TabloidProductsCombosOfertaResponse> tabloidProductsList,
  ) {
    return tabloidProductsList.map((tabloidProduct) {
      return CombosOfertaModel(
        idComboOferta: tabloidProduct.idComboOferta,
        descricao: tabloidProduct.descricao,
        status: tabloidProduct.status,
        menorDataVigencia: tabloidProduct.menorDataVigencia,
        produtos:
            tabloidProduct
                .produtos, // Supondo que ProdutosOfertasModel é compatível
        precoCombo: tabloidProduct.precoCombo,
        precoComboLiquido: tabloidProduct.precoComboLiquido,
        caminhoFoto: tabloidProduct.caminhoFoto,
        // tipoAgrupamentoLojas: tabloidProduct.tipoAgrupamentoLojas,
        locked: tabloidProduct.locked,
        qtdy: tabloidProduct.qtdy,
        qtdyController: TextEditingController(
          text: tabloidProduct.qtdy.toString(),
        ),
        focusNode: FocusNode(),
        states: tabloidProduct.states,
        distributorId: tabloidProduct.distributorId,
        priceOrder: tabloidProduct.priceOrder,
        totalOrder: tabloidProduct.totalOrder,
        orderComboQuantity: tabloidProduct.orderComboQuantity,
        validityComboQuantity: tabloidProduct.validityComboQuantity,
        orderComboLimit: tabloidProduct.orderComboLimit,
        validityComboLimit: tabloidProduct.validityComboLimit,
      );
    }).toList();
  }

  void updateFooter() {
    footer.totalGross = combosOferta
        .where((element) => element.qtdy! > 0)
        .map((e) => e.priceOrder ?? 0)
        .fold(0, (a, b) => a! + b);
    footer.totalNet = combosOferta
        .where((element) => element.qtdy! > 0)
        .map((e) => e.totalOrder ?? 0)
        .fold(0, (a, b) => a! + b);

    footer.totalUnits = combosOferta
        .where((element) => element.qtdy! > 0)
        .map((e) => e.qtdy ?? 0)
        .fold(0, (a, b) => a! + b);

    footer.qtyReal = combosOferta
        .where((element) => element.qtdy! > 0)
        .map((e) => e.qtdyReal ?? 0)
        .fold(0, (a, b) => a! + b);

    footer.totalApresentation =
        combosOferta.where((element) => element.qtdy! > 0).length;
    footer.discount =
        footer.totalGross != 0
            ? (((footer.totalGross! - footer.totalNet!) / footer.totalGross!) *
                100)
            : 0;
    update();
  }

  void setSearchFilter(String? v) {
    searchFilter = v;
    if (v != null) {
      if (combosOfertaFull.isNotEmpty) {
        combosOferta =
            combosOfertaFull
                .where((x) => x.descricao!.containsInsesitive(v))
                .toList();
      }
    } else {
      combosOferta = combosOfertaFull;
      searchController.text = "";
    }
    update();
  }

  //Zerar e atualizar a lista dos items removidos
  Future<void> setQtdyZero(CombosOfertaModel item) async {
    item.qtdy = 1;

    item.priceOrder = item.precoCombo! * item.qtdy!;
    item.totalOrder = item.precoComboLiquido! * item.qtdy!;
    item.qtdy = 0;
    item.qtdyController!.text = item.qtdy.toString();
    item.qtdyReal = calcularQtdyReal(item.produtos!, item);

    updateFooter();
  }

  Future<void> setQtdyEdit(CombosOfertaModel item, int value) async {
    item.qtdy = value;

    if (item.orderComboLimit == true && item.qtdy! > item.orderComboQuantity!) {
      SnackbarCustom.snackbarError(
        "Você adicionou quantidade maior que o limite para esta oferta, a quantidade foi definida automaticamente para o máximo permitido!",
      );
      item.qtdy = item.orderComboQuantity!;
    }
    if (item.validityComboLimit == true &&
        item.qtdy! > item.validityComboQuantity!) {
      SnackbarCustom.snackbarError(
        "Você adicionou quantidade maior que o limite de para esta oferta, a quantidade foi definida automaticamente para o máximo permitido",
      );
      item.qtdy = item.validityComboQuantity!;
    }

    item.qtdyReal = calcularQtdyReal(item.produtos!, item);

    item.qtdyController!.text = item.qtdy.toString();
    item.priceOrder = item.precoCombo! * item.qtdy!;
    item.totalOrder = item.precoComboLiquido! * item.qtdy!;
    updateFooter();
    await saveOrderLocal();
  }

  bool isLimitOver(CombosOfertaModel item) {
    if (item.orderComboLimit == true && item.orderComboQuantity! == 0) {
      return true;
    }
    if (item.validityComboLimit == true && item.validityComboQuantity! == 0) {
      return true;
    }

    return false;
  }

  Future<void> setQtdyUp(CombosOfertaModel item) async {
    item.qtdy = item.qtdy! + 1;

    if (item.orderComboLimit == true && item.qtdy! > item.orderComboQuantity!) {
      SnackbarCustom.snackbarError(
        "Você adicionou quantidade maior que o limite para esta oferta, a quantidade foi definida automaticamente para o máximo permitido!",
      );
      item.qtdy = item.orderComboQuantity!;
    }
    if (item.validityComboLimit == true &&
        item.qtdy! > item.validityComboQuantity!) {
      SnackbarCustom.snackbarError(
        "Você adicionou quantidade maior que o limite de para esta oferta, a quantidade foi definida automaticamente para o máximo permitido",
      );
      item.qtdy = item.validityComboQuantity!;
    }
    item.qtdyReal = calcularQtdyReal(item.produtos!, item);

    item.qtdyController!.text = item.qtdy.toString();
    item.priceOrder = item.precoCombo! * item.qtdy!;
    item.totalOrder = item.precoComboLiquido! * item.qtdy!;
    updateFooter();
    await saveOrderLocal();
  }

  int calcularQtdyReal(
    List<ProdutosOfertasModel> produtos,
    CombosOfertaModel item,
  ) {
    return (produtos.isNotEmpty)
        ? produtos
            .map((product) {
              if (product.idProdutoDUN != null &&
                  product.quantidadeDUN != null &&
                  product.quantidadeDUN! > 0) {
                return product.quantidade! *
                    item.qtdy! *
                    product.quantidadeDUN!;
              } else {
                return product.quantidade! * item.qtdy!;
              }
            })
            .reduce((a, b) => a + b)
        : 0;
  }

  Future<void> setQtdyDown(CombosOfertaModel item) async {
    item.qtdy = item.qtdy! - 1;

    if (item.qtdy! < 0) item.qtdy = 0;

    if (item.orderComboLimit == true && item.qtdy! > item.orderComboQuantity!) {
      SnackbarCustom.snackbarError(
        "Você adicionou quantidade maior que o limite para esta oferta, a quantidade foi definida automaticamente para o máximo permitido!",
      );
      item.qtdy = item.orderComboQuantity!;
    }

    if (item.validityComboLimit == true &&
        item.qtdy! > item.validityComboQuantity!) {
      SnackbarCustom.snackbarError(
        "Você adicionou quantidade maior que o limite de para esta oferta, a quantidade foi definida automaticamente para o máximo permitido",
      );
      item.qtdy = item.validityComboQuantity!;
    }
    item.qtdyReal = calcularQtdyReal(item.produtos!, item);

    item.qtdyController!.text = item.qtdy.toString();
    item.priceOrder = item.precoCombo! * item.qtdy!;
    item.totalOrder = item.precoComboLiquido! * item.qtdy!;
    updateFooter();
    await saveOrderLocal();
  }

  Future<void> advance() async {
    return trace('advance', () async {
      var (leaveAction, subAction) = dynatraceAction.subActionReport("advance");
      try {
        ordersResumeController.cartCombosOfertaList =
            combosOferta.where((element) => element.qtdy! > 0).toList();

        appLog(
          "Combos selecionados",
          data: {"combo": ordersResumeController.cartCombosOfertaList},
        );

        if (globalParams.getTypeOrderId() == TyperOrderEnum.especial) {
          final minOrderValue =
              globalParams
                  .getCurrentDistributor()!
                  .distribuidor!
                  .valorMinimoDePedido!;

          final totalProducts = ordersResumeController.cartProductsList
              .fold<double>(
                0.0,
                (previousValue, element) =>
                    previousValue + (element.totalOrder ?? 0.0),
              );
          final totalGeral = totalProducts + footer.totalNet!;
          if (totalGeral < minOrderValue) {
            await Dialogs.info(
              AppStrings.attention,
              AppStrings.orderSpecialMinValueMessage(
                minOrderValue.formatReal(),
                totalGeral.formatReal(),
              ),
              buttonName: "Entendi".toUpperCase(),
            );
            return;
          }
        }

        if (ordersResumeController.cartProductsList.isEmpty &&
            ordersResumeController.cartCombosOfertaList.isEmpty) {
          await Dialogs.info(
            AppStrings.attention,
            AppStrings.orderNoProducts,
            buttonName: "Entendi".toUpperCase(),
          );
          return;
        }
        appLogWithDynatrace(
          subAction,
          "Redirecionando para o resumo de pedidos",
        );
        await ordersResumeController.initialize();
        Get.toNamed(RoutesPath.ordersResume)?.then((value) {
          updateFooter();
          update();
        });
      } catch (e, s) {
        SnackbarCustom.snackbarError(e.toString());
        appErrorWithDynatrace(subAction, e, s);
        rethrow;
      } finally {
        leaveAction();
      }
    });
  }

  double getComboProductPrice(
    ProdutosOfertasModel product,
    int comboQtdy,
    bool isTotal,
  ) {
    double result = 0;

    final baseValue =
        isTotal ? (product.precoLiquido ?? 0) : (product.preco ?? 0);
    final qtdy = (product.quantidade ?? 0);
    final factor =
        (product.precoDistribuidor == true && product.quantidadeDUN != null)
            ? qtdy
            : qtdy * comboQtdy;

    result = baseValue * factor;

    result = double.parse((result + 0.005).toStringAsFixed(2));

    return result;
  }

  Future<void> saveOrderLocal() async {
    if (!ordersController.isEditOrder && !globalParams.order.orderLocalFinish) {
      // Atualiza ou cria um novo pedido local
      final orderData = OrdersLocalDataModel(
        storeId: globalParams.getCurrentStore()!.idLoja!,
        combosOferta:
            combosOferta.where((element) => element.qtdy! > 0).toList(),
        productListFull: globalParams.order.orderLocal?.productListFull,
        parameters: globalParams.order.orderParameters,
        clientNumber: globalParams.order.orderLocal?.clientNumber,
        observation: globalParams.order.orderLocal?.observation,
      );

      await orderData.saveOrder(
        storeId: globalParams.getCurrentStore()!.idLoja!,
        typeOrder: globalParams.order.orderParameters.typeOrderId!,
        hashCode:
            globalParams.order.orderParameters.typeOrderId! ==
                    TyperOrderEnum.especial
                ? "2:${ordersTabloidController.tabloidId}"
                : null,
      );

      // Atualiza o controle de pedidos locais
      globalParams.order.orderLocal = orderData;
    }
  }
}
